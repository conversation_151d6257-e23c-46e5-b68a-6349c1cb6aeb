@extends('admin.layouts.master_admin')

@section('page_last_name')
{{config('app.name')}} | Pricing
@endsection

@section('content')
    <!-- Content Header (Page header) -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Edit Pricing</h1>
                </div>
            </div>
        </div><!-- /.container-fluid -->
    </section>
    
    <!--Begin Content-->
    <section class="content">

        <form action="" method="post" id="add_business_term_form" enctype="multipart/form-data" data-parsley-validate>
            @csrf
            <fieldset>

                <div class="am-selected-media">
                    <span id="error-select-media"></span>
                    <ul class="media-box"> 
        
                        <li class="">
                            <input type="radio" name="media" value="Instagram" required @if($result[0]->media == 'Instagram') checked @endif  >
                            <img src="{{ asset('/') }}assets/front-end/images/icons/new_social_media_instagram.png" class=" icon" alt="">
                        </li>
                        <li class="">
                            <input type="radio" name="media" value="Facebook" required @if($result[0]->media == 'Facebook') checked @endif >
                            <img src="{{ asset('/') }}assets/front-end/images/icons/new_social_media_facebook.png" class=" icon" alt="">
                        </li>
                        <li class="">
                            <input type="radio" name="media" value="Tiktok" required @if($result[0]->media == 'Tiktok') checked @endif >
                            <img src="{{ asset('/') }}assets/front-end/images/icons/new_social_media_tiktok.png" class=" icon" alt="">
                        </li>
                        <li class="">
                            <input type="radio" name="media" value="Youtube" required @if($result[0]->media == 'Youtube') checked @endif >
                            <img src="{{ asset('/') }}assets/front-end/images/icons/new_social_media_youtube.png" class=" icon" alt="">
                        </li>
                        <li class="">
                            <input type="radio" name="media" value="Twitter" required @if($result[0]->media == 'Twitter') checked @endif >
                            <img src="{{ asset('/') }}assets/front-end/images/icons/new_social_media_twitter.png" class=" icon" alt="">
                        </li>
                    </ul>
                </div>

                <div class="form-group border-box d-flex">
                    <div class="one-line ">
                        <div class="custom-checkbox">
                            <input type="radio" name="type" value="Boost me" id="boost-me" required @if($result[0]->type == 'Boost me') checked @endif >
                            <label for="boost-me">Boost me</label>
                        </div>
                        <div class="custom-checkbox">
                            <input type="radio" name="type" value="Reaction-Video" id="reaction-video" required @if($result[0]->type == 'Reaction-Video') checked @endif >
                            <label for="reaction-video">Reaction-Video</label>
                        </div>
                        <div class="custom-checkbox">
                            <input type="radio" name="type" value="Survey" id="survey" required @if($result[0]->type == 'Survey') checked @endif >
                            <label for="survey">Survey</label>
                        </div>
                    </div>
                </div>

                <div class="form-group"> 
                    <label class="cntr-width" for="exampleInputEmail4">Country </label>
                    <label class="cntr-fr" for="exampleInputEmail4">Follower-Range</label>
                    <label class="cntr-cpt" for="exampleInputEmail4">CPT</label>
                </div>

                <div class="task-list-admin">
                    <div class="take-list d-flex block optionBox">  
                        <input class="country" type="text" name="country[]" value="Standard" readonly> 
                        <select name="range[]" class="range" required>
                            <option @foreach($result as $row) @if($row->country == 'Standard') @if($row->range == 'All') Selected @endif @endif @endforeach >All</option>
                        </select>
                        <input type="number" class="add-price form-control" name="cpt[]" placeholder="Enter pricing" required=""   @foreach($result as $row) @if($row->country == 'Standard') value="{{$row->cpt}}" @endif @endforeach
                        >    
                    </div>
                </div>
                    

                @foreach($result as $row) @if($row->country != 'Standard') 
                <div class="take-list d-flex block optionBox"> 
                    <select  class="country" name="country[]"     >
                        <option value="">Select Country</option>
                        @foreach($countries as $country)
                        <option @if($row->country == $country->id) selected @endif value="{{$country->id}}">{{$country->name}}</option>
                        @endforeach 
                    </select> 
                    <select name="range[]" class="range" required>
                        <option @if($row->range == 'All') Selected @endif >All</option>
                        <option @if($row->range == '5') Selected @endif   >5</option>
                        <option  @if($row->range == '500') Selected @endif  >500</option>
                        <option @if($row->range == '1000') Selected @endif   >1000</option>
                        <option @if($row->range == '10000') Selected @endif   >10000</option>
                        <option  @if($row->range == '50000') Selected @endif  >50000</option>
                        <option  @if($row->range == '100000') Selected @endif  >100000</option>
                        <option  @if($row->range == '500000') Selected @endif  >500000</option>
                        <option  @if($row->range == '1000000+') Selected @endif  >1000000+</option>
                    </select>
                    <input type="number" class="add-price form-control" name="cpt[]" placeholder="Enter pricing" required="" value="{{$row->cpt}}"  >  
                    <button type="button" class="remove working-icon"><img src="{{ asset('/') }}assets/front-end/images/icons/admin-remove-icon.png" alt=""></button>
                </div>
                @endif @endforeach

                <div class="d-flex">
                    <button type="button" class="add_counntry working-icon mr-2"><img src="{{ asset('/') }}assets/front-end/images/icons/admin-add-icon.png" alt=""></button> 
                        
                    <input type="submit" class="btn btn-danger" value="Update">
                </div>

            </fieldset>
        </form>

    </section>
    <!-- /.content -->
@endsection
@section('admin_script_codes')
 
 <script type="text/javascript">
$('.add_counntry').click(function() {
    $('.block:last').after('<div class="take-list d-flex block optionBox"> \
                            <select  class="country" name="country[]"     >\
                              <option value="">Select Country</option>\
                              @foreach($countries as $country)\
                              <option value="{{$country->id}}">{{$country->name}}</option>\
                              @endforeach \
                            </select>\
                            <select name="range[]" class="range" required>\
                                <option>All</option>\
                                <option  >5</option>\
                                <option  >500</option>\
                                <option  >1000</option>\
                                <option  >10000</option>\
                                <option  >50000</option>\
                                <option  >100000</option>\
                                <option  >500000</option>\
                                <option  >1000000+</option>\
                            </select>\
                            <input type="number" class="add-price form-control" name="cpt[]" placeholder="Enter pricing" required="" >\
                            <button type="button" class="remove working-icon"><img src="{{ asset('/') }}assets/front-end/images/icons/admin-remove-icon.png" alt=""></button></div>');
});
$(document).on('click', '.remove', function() {
    $(this).closest('div.take-list').remove();
 });

 </script>
@endsection
    
