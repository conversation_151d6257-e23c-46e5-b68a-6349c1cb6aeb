@extends('admin.layouts.master_admin')

@section('page_last_name')
{{config('app.name')}} | Paused Campaigns 
@endsection

@section('content')
    <!-- Content Header (Page header) -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Paused Campaigns</h1>
                </div>
            </div>
        </div><!-- /.container-fluid -->
    </section>
    
    <!--Begin Content-->
    <section class="content">

        <!-- Default box -->
        <div class="card">
            <div class="card-header">

                <div class="card-tools">
                    <button type="button" class="btn btn-tool" data-card-widget="collapse" data-toggle="tooltip"
                            title="Collapse">
                        <i class="fas fa-minus"></i></button>
                    <!-- <button type="button" class="btn btn-tool" data-card-widget="remove" data-toggle="tooltip"
                            title="Remove">
                        <i class="fas fa-times"></i></button> -->
                </div>
            </div>
            <div class="card-body">
                <form action="" method="post" id="add_business_term_form" enctype="multipart/form-data" data-parsley-validate>
                @csrf 
                    <table class="table" id="example1">
                        <thead>
                        <tr>
                            <th class="column-title">Created At</th>
                            <th class="column-title">Campaign ID</th>
                            <th class="column-title">Influencer</th>
                            <th class="column-title">Description</th>  
                            <th class="column-title">Social Media Link</th>  
                            <th class="column-title">Phone Number</th>
                            <th class="column-title">Status</th> 
                        </tr>
                        </thead>
                        <tbody>
                        <?php $i = 1;  ?>
                        @foreach($result as $row)
                            <tr>

                                <td>{{ @$row->created_at }}</td>
                                <td>{{ @$row->influencer_request_details->compaign_id }}</td>
                                <td>
                                    @php
                                        $user = null;
                                        $influencer = null;
                                        
                                        if (isset($row->influencer_request_details) && 
                                            isset($row->influencer_request_details->influencer_detail_id)) {
                                            
                                            $influencer = App\Models\InfluencerDetail::where('id', 
                                                $row->influencer_request_details->influencer_detail_id)->first();
                                                
                                            if ($influencer && isset($influencer->user_id)) {
                                                $user = App\Models\User::where('id', $influencer->user_id)->first();
                                            }
                                        }
                                    @endphp
                                    @if($user)
                                        {{ ucfirst($user->first_name) }} {{ ucfirst($user->last_name) }}
                                    @else
                                        <span class="text-danger">User information temporarily unavailable</span>
                                    @endif
                                </td>
                                <td>{{ @$row->description }}</td> 
                                <td> {{ @$row->social_media_link }} </td>
                                <td> {{ @$row->phone_no }} </td>
                                <td> {{ @$row->status }} </td>
                            </tr>
                            <?php $i++; ?>
                        @endforeach
                        
                        </tbody>
                    </table>
                </form>
            </div>
            <!-- /.card-body -->
            <div class="card-footer">
                {{--Footer--}}
            </div>
            <!-- /.card-footer-->
        </div>
        <!-- /.card -->

    </section>
    <!-- /.content -->
@endsection
@section('admin_script_codes')
 <script type="text/javascript">
 $(document).on('change','.update_status',function(){
    var status     = $(this).val();
    var rowId      = $(this).attr('data-task');
    $.ajax({
    url: "{{URL::to('/admin/update-complaint-status')}}/"+rowId+"/"+status,  
    method: 'GET',  
    }).done(function (data) {    
    toastr.success(data.msg);
    setTimeout(function () {
        location.reload();
    }, 1000)
   }); 
 })
 </script>
@endsection
    
