<?php

namespace App\Helpers;

class HardcodedPricingHelper
{
    /**
     * Hardcoded pricing data for Survey & Boost Me campaigns (Story posts)
     * Structure: [follower_range => [estimated_reach_rate, cpt]]
     */
    private static $storyPricing = [
        'nano' => [
            'min_followers' => 1000,
            'max_followers' => 10000,
            'estimated_reach_rate' => 6.0, // 6%
            'cpt' => 5.00
        ],
        'micro' => [
            'min_followers' => 10000,
            'max_followers' => 50000,
            'estimated_reach_rate' => 5.0, // 5%
            'cpt' => 4.50
        ],
        'mid_tier' => [
            'min_followers' => 50000,
            'max_followers' => 500000,
            'estimated_reach_rate' => 4.0, // 4%
            'cpt' => 3.00
        ],
        'macro' => [
            'min_followers' => 500000,
            'max_followers' => 1000000,
            'estimated_reach_rate' => 3.0, // 3%
            'cpt' => 2.50
        ],
        'mega' => [
            'min_followers' => 1000000,
            'max_followers' => PHP_INT_MAX,
            'estimated_reach_rate' => 2.0, // 2%
            'cpt' => 2.00
        ]
    ];

    /**
     * Hardcoded pricing data for Reaction Video campaigns (Reel posts)
     * Structure: [follower_range => [estimated_reach_rate, cpt]]
     */
    private static $reelPricing = [
        'nano' => [
            'min_followers' => 1000,
            'max_followers' => 10000,
            'estimated_reach_rate' => 11.0, // 11%
            'cpt' => 14.00
        ],
        'micro' => [
            'min_followers' => 10000,
            'max_followers' => 50000,
            'estimated_reach_rate' => 10.0, // 10%
            'cpt' => 13.00
        ],
        'mid_tier' => [
            'min_followers' => 50000,
            'max_followers' => 500000,
            'estimated_reach_rate' => 9.0, // 9%
            'cpt' => 12.00
        ],
        'macro' => [
            'min_followers' => 500000,
            'max_followers' => 1000000,
            'estimated_reach_rate' => 8.0, // 8%
            'cpt' => 11.00
        ],
        'mega' => [
            'min_followers' => 1000000,
            'max_followers' => PHP_INT_MAX,
            'estimated_reach_rate' => 7.0, // 7%
            'cpt' => 10.00
        ]
    ];

    /**
     * Get pricing data for a specific campaign type and follower count
     *
     * @param string $campaignType ('Boost me', 'Survey', 'Reaction video')
     * @param int $followers
     * @return array|null ['category' => string, 'estimated_reach_rate' => float, 'cpt' => float]
     */
    public static function getPricingData(string $campaignType, int $followers): ?array
    {
        $pricingTable = self::getPricingTable($campaignType);
        
        if (!$pricingTable) {
            return null;
        }

        foreach ($pricingTable as $category => $data) {
            if ($followers >= $data['min_followers'] && $followers < $data['max_followers']) {
                return [
                    'category' => $category,
                    'estimated_reach_rate' => $data['estimated_reach_rate'],
                    'cpt' => $data['cpt'],
                    'min_followers' => $data['min_followers'],
                    'max_followers' => $data['max_followers']
                ];
            }
        }

        return null;
    }

    /**
     * Get estimated reach rate for a campaign type and follower count
     *
     * @param string $campaignType
     * @param int $followers
     * @return float|null
     */
    public static function getEstimatedReachRate(string $campaignType, int $followers): ?float
    {
        $pricingData = self::getPricingData($campaignType, $followers);
        return $pricingData ? $pricingData['estimated_reach_rate'] : null;
    }

    /**
     * Get CPT (Cost per Thousand) for a campaign type and follower count
     *
     * @param string $campaignType
     * @param int $followers
     * @return float|null
     */
    public static function getCpt(string $campaignType, int $followers): ?float
    {
        $pricingData = self::getPricingData($campaignType, $followers);
        return $pricingData ? $pricingData['cpt'] : null;
    }

    /**
     * Get influencer category based on follower count
     *
     * @param int $followers
     * @return string|null
     */
    public static function getInfluencerCategory(int $followers): ?string
    {
        // Use story pricing table as reference (same categories for both)
        foreach (self::$storyPricing as $category => $data) {
            if ($followers >= $data['min_followers'] && $followers < $data['max_followers']) {
                return $category;
            }
        }

        return null;
    }

    /**
     * Get all available categories with their follower ranges
     *
     * @return array
     */
    public static function getAllCategories(): array
    {
        $categories = [];
        
        foreach (self::$storyPricing as $category => $data) {
            $categories[$category] = [
                'name' => self::getCategoryDisplayName($category),
                'min_followers' => $data['min_followers'],
                'max_followers' => $data['max_followers'] === PHP_INT_MAX ? '∞' : $data['max_followers'],
                'story_reach_rate' => self::$storyPricing[$category]['estimated_reach_rate'],
                'story_cpt' => self::$storyPricing[$category]['cpt'],
                'reel_reach_rate' => self::$reelPricing[$category]['estimated_reach_rate'],
                'reel_cpt' => self::$reelPricing[$category]['cpt'],
            ];
        }

        return $categories;
    }

    /**
     * Get display name for category
     *
     * @param string $category
     * @return string
     */
    public static function getCategoryDisplayName(string $category): string
    {
        $displayNames = [
            'nano' => 'Nano',
            'micro' => 'Micro',
            'mid_tier' => 'Mid-tier',
            'macro' => 'Macro',
            'mega' => 'Mega / Celebrity'
        ];

        return $displayNames[$category] ?? ucfirst($category);
    }

    /**
     * Check if hardcoded pricing should be used (always true for now)
     *
     * @return bool
     */
    public static function shouldUseHardcodedPricing(): bool
    {
        return true; // Always use hardcoded pricing for now
    }

    /**
     * Get the appropriate pricing table based on campaign type
     *
     * @param string $campaignType
     * @return array|null
     */
    private static function getPricingTable(string $campaignType): ?array
    {
        switch (strtolower($campaignType)) {
            case 'boost me':
            case 'survey':
                return self::$storyPricing;
            
            case 'reaction video':
                return self::$reelPricing;
            
            default:
                return null;
        }
    }

    /**
     * Get pricing summary for debugging
     *
     * @param string $campaignType
     * @param int $followers
     * @return array
     */
    public static function getPricingSummary(string $campaignType, int $followers): array
    {
        $pricingData = self::getPricingData($campaignType, $followers);
        
        return [
            'campaign_type' => $campaignType,
            'followers' => $followers,
            'category' => $pricingData ? self::getCategoryDisplayName($pricingData['category']) : 'Unknown',
            'estimated_reach_rate' => $pricingData ? $pricingData['estimated_reach_rate'] : null,
            'cpt' => $pricingData ? $pricingData['cpt'] : null,
            'follower_range' => $pricingData ? [
                'min' => $pricingData['min_followers'],
                'max' => $pricingData['max_followers'] === PHP_INT_MAX ? '∞' : $pricingData['max_followers']
            ] : null,
            'using_hardcoded_pricing' => true
        ];
    }

    /**
     * Validate if follower count is within supported range
     *
     * @param int $followers
     * @return bool
     */
    public static function isFollowerCountSupported(int $followers): bool
    {
        return $followers >= 1000; // Minimum supported follower count
    }

    /**
     * Get minimum supported follower count
     *
     * @return int
     */
    public static function getMinimumFollowerCount(): int
    {
        return 1000;
    }
}
