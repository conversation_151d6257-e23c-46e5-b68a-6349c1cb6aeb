<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use App\Models\User;

class DebugLogViewer extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'debug:log-viewer';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Debug log-viewer configuration and access issues';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('=== Log Viewer Debug Information ===');
        $this->newLine();

        // 1. Check middleware registration
        $this->info('1. Middleware Registration:');
        $middlewares = app('router')->getMiddleware();
        if (isset($middlewares['admin.only'])) {
            $this->info('✅ admin.only middleware is registered');
            $this->info('   Class: ' . $middlewares['admin.only']);
        } else {
            $this->error('❌ admin.only middleware is NOT registered');
        }
        $this->newLine();

        // 2. Check log-viewer configuration
        $this->info('2. Log Viewer Configuration:');
        try {
            $config = config('log-viewer');
            if ($config) {
                $this->info('✅ Log viewer config loaded');
                $this->info('   Enabled: ' . ($config['enabled'] ? 'Yes' : 'No'));
                $this->info('   Require auth in production: ' . ($config['require_auth_in_production'] ? 'Yes' : 'No'));
                $this->info('   Middleware: ' . json_encode($config['middleware'] ?? []));
                $this->info('   API Middleware: ' . json_encode($config['api_middleware'] ?? []));
            } else {
                $this->error('❌ Log viewer config not found');
            }
        } catch (\Exception $e) {
            $this->error('❌ Error loading log-viewer config: ' . $e->getMessage());
        }
        $this->newLine();

        // 3. Check admin users
        $this->info('3. Admin Users:');
        try {
            $adminUsers = User::where('user_type', 'admin')->get(['id', 'email', 'user_type']);
            if ($adminUsers->count() > 0) {
                $this->info('✅ Found ' . $adminUsers->count() . ' admin user(s):');
                foreach ($adminUsers as $user) {
                    $this->info('   - ID: ' . $user->id . ', Email: ' . $user->email);
                }
            } else {
                $this->error('❌ No admin users found');
            }
        } catch (\Exception $e) {
            $this->error('❌ Error checking admin users: ' . $e->getMessage());
        }
        $this->newLine();

        // 4. Check routes
        $this->info('4. Log Viewer Routes:');
        $routes = Route::getRoutes();
        $logViewerRoutes = [];
        foreach ($routes as $route) {
            if (str_contains($route->uri(), 'log-viewer')) {
                $logViewerRoutes[] = [
                    'uri' => $route->uri(),
                    'methods' => implode('|', $route->methods()),
                    'middleware' => $route->middleware(),
                ];
            }
        }

        if (!empty($logViewerRoutes)) {
            $this->info('✅ Found ' . count($logViewerRoutes) . ' log-viewer route(s):');
            foreach ($logViewerRoutes as $route) {
                $this->info('   - ' . $route['methods'] . ' ' . $route['uri']);
                $this->info('     Middleware: ' . json_encode($route['middleware']));
            }
        } else {
            $this->error('❌ No log-viewer routes found');
        }
        $this->newLine();

        // 5. Test middleware class
        $this->info('5. Middleware Class Test:');
        try {
            $middlewareClass = 'App\Http\Middleware\AdminOnly';
            if (class_exists($middlewareClass)) {
                $this->info('✅ AdminOnly middleware class exists');
                $reflection = new \ReflectionClass($middlewareClass);
                $this->info('   File: ' . $reflection->getFileName());
            } else {
                $this->error('❌ AdminOnly middleware class not found');
            }
        } catch (\Exception $e) {
            $this->error('❌ Error checking middleware class: ' . $e->getMessage());
        }
        $this->newLine();

        // 6. Environment check
        $this->info('6. Environment:');
        $this->info('   APP_ENV: ' . config('app.env'));
        $this->info('   APP_DEBUG: ' . (config('app.debug') ? 'true' : 'false'));
        $this->info('   LOG_VIEWER_ENABLED: ' . (config('log-viewer.enabled') ? 'true' : 'false'));
        $this->newLine();

        // 7. Suggestions
        $this->info('7. Troubleshooting Suggestions:');
        
        if (!isset($middlewares['admin.only'])) {
            $this->warn('   → Register admin.only middleware in app/Http/Kernel.php');
        }
        
        if (!User::where('user_type', 'admin')->exists()) {
            $this->warn('   → Create at least one admin user');
        }
        
        $this->info('   → Try: php artisan config:clear');
        $this->info('   → Try: composer dump-autoload');
        $this->info('   → Check if files are properly deployed');
        
        $this->newLine();
        $this->info('=== Debug Complete ===');

        return 0;
    }
}
