<?php

namespace App\Services;

use App\Models\AdminPricing;
use App\Models\SocialConnect;
use App\Services\AverageReachCalculatorService;
use App\Helpers\HardcodedPricingHelper;
use Illuminate\Support\Facades\Log;

class ReachBasedPriceCalculatorService
{
    protected $averageReachCalculator;

    public function __construct(AverageReachCalculatorService $averageReachCalculator)
    {
        $this->averageReachCalculator = $averageReachCalculator;
    }

    /**
     * Calculate reach-adjusted price for a campaign type
     *
     * @param int $userId
     * @param string $media Social media platform
     * @param string $campaignType Campaign type ('Boost me', 'Survey', 'Reaction video')
     * @param float $basePriceAfterGamification Base price after CPT and gamification multiplier
     * @param string $country User's country
     * @return array ['price' => float, 'reach_multiplier' => float, 'used_reach_pricing' => bool, 'debug_info' => array]
     */
    public function calculateReachAdjustedPrice(
        int $userId, 
        string $media, 
        string $campaignType, 
        float $basePriceAfterGamification,
        string $country = 'Standard'
    ): array {
        // Get average reach for this campaign type
        $averageReach = $this->averageReachCalculator->calculateAverageReachByCampaignType($userId, $campaignType);
        
        // If average reach is 0, skip reach-based pricing
        if ($averageReach === 0) {
            return [
                'price' => $basePriceAfterGamification,
                'reach_multiplier' => 1.0,
                'used_reach_pricing' => false,
                'debug_info' => [
                    'reason' => 'Average reach is 0 for campaign type: ' . $campaignType,
                    'average_reach' => $averageReach,
                ]
            ];
        }

        // Get follower count first to determine pricing tier
        $socialConnect = SocialConnect::where('user_id', $userId)->where('media', strtolower($media))->first();

        if (!$socialConnect || $socialConnect->followers <= 0) {
            return [
                'price' => $basePriceAfterGamification,
                'reach_multiplier' => 1.0,
                'used_reach_pricing' => false,
                'debug_info' => [
                    'reason' => 'No valid social media connection or followers',
                    'social_connect_found' => $socialConnect !== null,
                    'followers' => $socialConnect ? $socialConnect->followers : 0,
                ]
            ];
        }

        // Check if follower count is supported by hardcoded pricing
        if (!HardcodedPricingHelper::isFollowerCountSupported($socialConnect->followers)) {
            return [
                'price' => $basePriceAfterGamification,
                'reach_multiplier' => 1.0,
                'used_reach_pricing' => false,
                'debug_info' => [
                    'reason' => 'Follower count below minimum supported range',
                    'followers' => $socialConnect->followers,
                    'minimum_required' => HardcodedPricingHelper::getMinimumFollowerCount(),
                ]
            ];
        }

        // Get estimated reach percentage from hardcoded pricing
        $estimatedReachPercentage = HardcodedPricingHelper::getEstimatedReachRate($campaignType, $socialConnect->followers);

        if ($estimatedReachPercentage === null) {
            return [
                'price' => $basePriceAfterGamification,
                'reach_multiplier' => 1.0,
                'used_reach_pricing' => false,
                'debug_info' => [
                    'reason' => 'No hardcoded pricing data found for campaign type',
                    'campaign_type' => $campaignType,
                    'followers' => $socialConnect->followers,
                ]
            ];
        }

        // Social connect already retrieved above, no need to fetch again

        // Calculate reach multiplier: Average Reach / (Followers * Estimated Reach %)
        $expectedReach = $socialConnect->followers * ($estimatedReachPercentage / 100);
        $reachMultiplier = $averageReach / $expectedReach;
        
        // Apply reach multiplier to the price
        $adjustedPrice = $basePriceAfterGamification * $reachMultiplier;

        // Get pricing summary for debug info
        $pricingSummary = HardcodedPricingHelper::getPricingSummary($campaignType, $socialConnect->followers);

        return [
            'price' => $adjustedPrice,
            'reach_multiplier' => $reachMultiplier,
            'used_reach_pricing' => true,
            'debug_info' => [
                'average_reach' => $averageReach,
                'followers' => $socialConnect->followers,
                'estimated_reach_percentage' => $estimatedReachPercentage,
                'expected_reach' => $expectedReach,
                'base_price_after_gamification' => $basePriceAfterGamification,
                'adjusted_price' => $adjustedPrice,
                'campaign_type' => $campaignType,
                'media' => $media,
                'pricing_summary' => $pricingSummary,
                'using_hardcoded_pricing' => true,
            ]
        ];
    }

    /**
     * Get estimated reach percentage from admin pricing configuration or hardcoded data
     *
     * @param string $media
     * @param string $campaignType
     * @param string $country
     * @param int $followers
     * @return float|null
     */
    protected function getEstimatedReachPercentageWithFallback(string $media, string $campaignType, string $country, int $followers): ?float
    {
        // First try to get from database (for future admin interface)
        $dbReachPercentage = $this->getEstimatedReachPercentage($media, $campaignType, $country);

        if ($dbReachPercentage !== null) {
            return $dbReachPercentage;
        }

        // Fallback to hardcoded pricing
        return HardcodedPricingHelper::getEstimatedReachRate($campaignType, $followers);
    }

    /**
     * Get estimated reach percentage from admin pricing configuration (database)
     *
     * @param string $media
     * @param string $campaignType
     * @param string $country
     * @return float|null
     */
    protected function getEstimatedReachPercentage(string $media, string $campaignType, string $country): ?float
    {
        // Map campaign types to admin pricing types
        $pricingType = $this->mapCampaignTypeToPricingType($campaignType);

        if (!$pricingType) {
            return null;
        }

        $adminPricing = AdminPricing::where('media', ucfirst($media))
            ->where('type', $pricingType)
            ->where(function ($query) use ($country) {
                $query->where('country', '=', $country)
                    ->orWhere('country', '=', 'Standard');
            })
            ->whereNotNull('estimated_reach')
            ->latest('id')
            ->first();

        return $adminPricing ? (float) $adminPricing->estimated_reach : null;
    }

    /**
     * Map campaign type to admin pricing type
     *
     * @param string $campaignType
     * @return string|null
     */
    protected function mapCampaignTypeToPricingType(string $campaignType): ?string
    {
        $mapping = [
            'Boost me' => 'Boost me',
            'Survey' => 'Survey',
            'Reaction video' => 'Reaction-Video',
        ];

        return $mapping[$campaignType] ?? null;
    }

    /**
     * Calculate reach statistics for debugging purposes
     *
     * @param int $userId
     * @param string $media
     * @param string $campaignType
     * @return array
     */
    public function getReachCalculationDebugInfo(int $userId, string $media, string $campaignType): array
    {
        $postType = AverageReachCalculatorService::CAMPAIGN_TYPE_TO_POST_TYPE[$campaignType] ?? null;
        
        if (!$postType) {
            return ['error' => 'Unknown campaign type: ' . $campaignType];
        }

        $reachStats = $this->averageReachCalculator->getReachStatistics($userId, $postType);
        $socialConnect = SocialConnect::where('user_id', $userId)->where('media', strtolower($media))->first();

        $estimatedReach = null;
        $pricingSummary = null;

        if ($socialConnect && $socialConnect->followers > 0) {
            $estimatedReach = $this->getEstimatedReachPercentageWithFallback($media, $campaignType, 'Standard', $socialConnect->followers);
            $pricingSummary = HardcodedPricingHelper::getPricingSummary($campaignType, $socialConnect->followers);
        }

        return [
            'reach_statistics' => $reachStats,
            'social_connect' => [
                'found' => $socialConnect !== null,
                'followers' => $socialConnect ? $socialConnect->followers : 0,
                'media' => $media,
            ],
            'estimated_reach_percentage' => $estimatedReach,
            'pricing_summary' => $pricingSummary,
            'campaign_type' => $campaignType,
            'post_type' => $postType,
            'using_hardcoded_pricing' => HardcodedPricingHelper::shouldUseHardcodedPricing(),
        ];
    }
}
