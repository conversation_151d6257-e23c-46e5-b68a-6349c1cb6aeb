<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AdminOnly
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // Check if user is authenticated with admin guard
        if (Auth::guard('admin')->check()) {
            return $next($request);
        }

        // Check if user is authenticated with web guard and is admin type
        if (Auth::guard('web')->check() && Auth::guard('web')->user()->user_type === 'admin') {
            return $next($request);
        }

        // If not admin, return 403 Forbidden
        abort(403, 'Access denied. Admin privileges required.');
    }
}
