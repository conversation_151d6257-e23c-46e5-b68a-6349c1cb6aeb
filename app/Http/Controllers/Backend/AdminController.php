<?php

namespace App\Http\Controllers\Backend;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Blog;
use App\Models\FaqTopic;
use App\Models\Faq;
use App\Models\CmsPage;
use App\Models\Country;
use App\Models\State;
use App\Models\Category;
use App\Models\SocialKeys;
use App\Models\City;
use App\Models\Mode;
use App\Models\Dialogue;
use App\Models\CampaignRequestTime;
use App\Models\Complaint;
use App\Models\AdminComission;
use App\Models\AdminHashtag;
use App\Models\InfluencerRequestDetail;
use App\Models\ContactSupport;
use App\Models\AdminGamification;
use App\Models\AdminPricing;
use App\Models\InfluencerDetail;
use App\Models\Task;
use App\Models\Statistic;
use App\Models\SmCampaign;
use App\Models\RequestTask;


use Auth;
use DB;
use Hash;
use File;
use Mail;
use App\Notifications\addNewCustomer;
use App\Notifications\addNewInfluencer;
use App\Notifications\ComplaintUpdate;
use App\Notifications\complaintCancelled;
use App\Notifications\RequestCancelInfluencer;

use App\Notifications\SendActivateUser;


use App\Jobs\NewaddNewCustomer;
use App\Jobs\NewaddNewInfluencer;
use App\Jobs\NewcomplaintCancelled;
use App\Jobs\NewComplaintUpdate;
use App\Jobs\NewRequestCancelInfluencer;
use App\Jobs\NewSendActivateUser;
use App\Jobs\NewwelcomeToTheClosedBeta;


use App\Jobs\NewcustomerComplaintwasAccepted;
use App\Jobs\NewcustomerComplaintwasRejected;


use Str;
use App;
use App\Http\Controllers\Controller;
use App\Jobs\NewCancelRefund;
use App\Models\PausedCampaign;
use App\Models\SocialPost;
use App\Notifications\CancelRefund;

class AdminController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth:admin');
    }
    /**
     * show dashboard.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $count = new \stdClass();
        $count->customers = User::whereUserType('customer')->count();
        $count->influencers = User::whereUserType('influencer')->count();
        return view('admin.dashboard', compact('count'));
    }

    public function profile()
    {
        return view('admin.settings.profile');
    }

    public function updateAdminProfile(Request $request)
    {

        $formData = request()->except(['_token']);

        if ($request->hasFile('profile_pic')) {
            $allowedfileExtension = ['jpeg', 'jpg', 'png', 'svg'];
            $file = $request->file('profile_pic');

            $extension = $file->getClientOriginalExtension();

            if (in_array($extension, $allowedfileExtension)) {

                $formData['profile_pic'] = $file->store('profile_pics', 'public');
            }
        }

        $user = User::find(['id' => Auth::id()])->first();

        if ($user->update($formData)) {
            return back()->with('success', 'You have successfully updated the profile.!');
        } else {
            return back()->with('error', 'Sorry there is an error while updating your profile.!');
        }
    }

    public function changePassword()
    {
        return view('admin.settings.change-password');
    }

    public function  updateAdminPassword(Request $request)
    {
        $user = DB::table('users')
            ->select('*')
            ->where('id', Auth::guard('admin')->id())
            ->first();

        if (!Hash::check($request->oldpassword, $user->password)) {
            return back()->with('error', 'Sorry your current password does not match.!');
        }

        $this->validate($request, ['password' => 'required|confirmed|min:8']);

        if ($request->password == $request->password_confirmation) {
            if ($user) {
                $password_updated = DB::table('users')
                    ->where('id',  Auth::guard('admin')->id())
                    ->update(['password' => Hash::make($request->password)]);

                if ($password_updated) {
                    return back()->with(['success' => 'Password is changed successfully.!']);
                } else {
                    return back()->with(['error' => 'There is an error while changing the password please try again later.!']);
                }
            }
        } else {
            return back()->with('error', 'New password do not matched with confirm password.!');
        }
    }

    public function fetchStates(Request $request)
    {
        $cities = DB::table('states')->leftjoin('cities',  'states.id', '=', 'cities.state_id')
            ->select('cities.*')
            ->where('states.country_id', $request->country)->get();
        $data = '<option value="">Select Cities</option>';
        foreach ($cities as $key => $row) {
            $data .= "<option value='" . $row->id . "'>" . $row->name . "</option>";
        }
        return $data;
    }
    public function manageCustomers()
    {
        $result = User::whereUserType('customer')->get();
        return view('admin.customers.manage', compact('result'));
    }

    public function addCustomer()
    {
        $countries = Country::get();
        $cities = City::get();
        return view('admin.customers.add', compact('countries', 'cities'));
    }

    public function saveCustomer(Request $request)
    {
        $formData = request()->except(['_token']);

        $password = str_random(10);

        $this->validate($request, [
            'first_name' => ['required', 'string', 'max:255'],
            'last_name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],

        ], [
            "first_name.required" => "Please enter :attribute",
            "last_name.required" => "Please enter :attribute",
            "email.required" => "Please enter :attribute"
        ]);

        $formData['password'] = Hash::make($password);
        $formData['user_type'] = 'customer';
        $formData['email_verified_at'] = date("Y-m-d");

        if ($request->hasFile('profile_pic')) {
            $allowedfileExtension = ['jpeg', 'jpg', 'png', 'svg'];
            $file = $request->file('profile_pic');

            $extension = $file->getClientOriginalExtension();

            if (in_array($extension, $allowedfileExtension)) {

                $formData['profile_pic'] = $file->store('profile_pics');
            }
        }
        $formData['status'] = 1;

        $customer = User::create($formData);

        //Notify the customer for the registration
        dispatch(new NewaddNewCustomer($customer, $password));

        // $customer->notify(new addNewCustomer($customer, $password));

        if ($customer->save()) {
            return redirect('/admin/manage-customers')->with('success', 'Customer added successfully.');
        } else {
            return redirect('/admin/manage-customers')->with('error', 'Sorry there is an error while adding customer. please try again.');
        }
    }

    public function editCustomer($id)
    {
        $row = User::find($id);
        $countries = Country::get();
        $states = State::find($row->state);
        $cities = City::get();
        return view('admin.customers.edit', compact('row', 'countries', 'states', 'cities'));
    }

    public function updateCustomer($id, Request $request)
    {
        $formData = request()->except(['_token']);

        $this->validate($request, [
            'first_name' => ['required', 'string', 'max:255'],
            'last_name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255'],

        ], [
            "first_name.required" => "Please enter :attribute",
            "last_name.required" => "Please enter :attribute",
            "email.required" => "Please enter :attribute",
        ]);

        // save the row to the database
        $duplicateEntry = User::whereEmail($formData['email'])->first();

        if ($duplicateEntry == '' || ($duplicateEntry != '' && $duplicateEntry->id == $id)) {
            if ($request->hasFile('profile_pic')) {
                $allowedfileExtension = ['jpeg', 'jpg', 'png', 'svg'];
                $file = $request->file('profile_pic');
                File::delete($formData['oldImageValue']);

                $extension = $file->getClientOriginalExtension();

                if (in_array($extension, $allowedfileExtension)) {

                    $formData['profile_pic'] = $file->store('profile_pics', 'public');
                }
            } else {
                $formData['profile_pic'] = $formData['oldImageValue'];
            }
            User::find($id)->update($formData);
        } else
            return redirect()->back()->with('error', 'Email already taken.');

        return redirect('/admin/manage-customers')->with('success', 'Customer updated successfully.');
    }

    public function deleteCustomer(Request $request)
    {
        $id = $request->input('id');

        if (User::find($id)->delete()) {
            return response()->json(['status' => 'success', 'msg' => 'You have successfully deleted customer']);
        } else {
            return response()->json(['status' => 'error', 'msg' => 'Sorry there is an error in deleting customer. Please try again later!']);
        }
    }

    public function manageInfluencers()
    {
        $result = User::whereUserType('influencer')->get();
        $categories = Category::get();
        return view('admin.influencers.manage', compact('result', 'categories'));
    }

    /**
     * Display all users with impersonation functionality
     */
    public function manageUsers(Request $request)
    {
        $query = User::query();

        // Filter by user type if specified
        if ($request->filled('user_type') && $request->user_type !== 'all') {
            $query->where('user_type', $request->user_type);
        }

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('first_name', 'LIKE', "%{$search}%")
                  ->orWhere('last_name', 'LIKE', "%{$search}%")
                  ->orWhere('email', 'LIKE', "%{$search}%")
                  ->orWhere('company_name', 'LIKE', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->where('flag', 1);
            } elseif ($request->status === 'inactive') {
                $query->where('flag', '!=', 1);
            }
        }

        $users = $query->orderBy('created_at', 'desc')->paginate(20);
        $userTypes = ['all' => 'All Users', 'admin' => 'Admins', 'customer' => 'Brands', 'influencer' => 'Influencers'];

        return view('admin.users.manage', compact('users', 'userTypes'));
    }

    public function addInfluencer()
    {
        $countries = Country::get();
        $cities = City::get();
        return view('admin.influencers.add', compact('countries', 'cities'));
    }

    public function saveInfluencer(Request $request)
    {
        $formData = request()->except(['_token']);

        $password = str_random(10);

        $this->validate($request, [
            'first_name' => ['required', 'string', 'max:255'],
            'last_name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],

        ], [
            "first_name.required" => "Please enter :attribute",
            "last_name.required" => "Please enter :attribute",
            "email.required" => "Please enter :attribute",
        ]);

        if ($request->hasFile('profile_pic')) {
            $allowedfileExtension = ['jpeg', 'jpg', 'png', 'svg'];
            $file = $request->file('profile_pic');

            $extension = $file->getClientOriginalExtension();

            if (in_array($extension, $allowedfileExtension)) {

                $formData['profile_pic'] = $file->store('profile_pics');
            }
        }

        $formData['password'] = Hash::make($password);
        $formData['user_type'] = 'influencer';
        $formData['email_verified_at'] = date("Y-m-d");
        $formData['status'] = 1;

        $rinkOperator = User::create($formData);

        //Notify the influencer for the registration
        dispatch(new NewaddNewInfluencer($customer, $password));

        // $rinkOperator->notify(new addNewInfluencer($rinkOperator, $password));

        if ($rinkOperator->save()) {
            return redirect('/admin/manage-influencers')->with('success', 'Rink Operator added successfully.');
        } else {
            return redirect('/admin/manage-influencers')->with('error', 'Sorry there is an error while adding influencer. please try again.');
        }
    }

    public function editInfluencer($id)
    {
        $row = User::find($id);
        $countries = Country::get();
        $states = State::find($row->state);
        $cities = City::get();
        return view('admin.influencers.edit', compact('row', 'countries', 'states', 'cities'));
    }

    public function updateInfluencer($id, Request $request)
    {
        $formData = request()->except(['_token']);

        $this->validate($request, [
            'first_name' => ['required', 'string', 'max:255'],
            'last_name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255'],

        ], [
            "first_name.required" => "Please enter :attribute",
            "last_name.required" => "Please enter :attribute",
            "email.required" => "Please enter :attribute",
        ]);

        // save the row to the database
        $duplicateEntry = User::whereEmail($formData['email'])->first();

        if ($duplicateEntry == '' || ($duplicateEntry != '' && $duplicateEntry->id == $id)) {
            if ($request->hasFile('profile_pic')) {
                $allowedfileExtension = ['jpeg', 'jpg', 'png', 'svg'];
                $file = $request->file('profile_pic');
                File::delete($formData['oldImageValue']);

                $extension = $file->getClientOriginalExtension();

                if (in_array($extension, $allowedfileExtension)) {

                    $formData['profile_pic'] = $file->store('profile_pics', 'public');
                }
            } else {
                $formData['profile_pic'] = $formData['oldImageValue'];
            }
            User::find($id)->update($formData);
        } else
            return redirect()->back()->with('error', 'Email already taken.');

        return redirect('/admin/manage-influencers')->with('success', 'Rink Operator updated successfully.');
    }

    public function deleteInfluencer(Request $request)
    {
        $id = $request->input('id');

        if (User::find($id)->delete()) {
            return response()->json(['status' => 'success', 'msg' => 'You have successfully deleted influencer']);
        } else {
            return response()->json(['status' => 'error', 'msg' => 'Sorry there is an error in deleting influencer. Please try again later!']);
        }
    }

    public function manageBlogs()
    {
        $result = Blog::all();
        return view('admin.blogs.manage', compact('result'));
    }

    public function addBlog()
    {
        return view('admin.blogs.add');
    }

    public function saveBlog(Request $request)
    {
        $formData = request()->except(['_token']);

        $this->validate($request, [
            'title' => ['required', 'string', 'max:255'],
            'description' => ['required', 'string'],
        ], [
            "title.required" => "Please enter :attribute",
            "description.required" => "Please enter :attribute",
        ]);

        if ($request->hasFile('image')) {
            $allowedfileExtension = ['jpeg', 'jpg', 'png', 'svg'];
            $file = $request->file('image');

            $extension = $file->getClientOriginalExtension();

            if (in_array($extension, $allowedfileExtension)) {

                $formData['image'] = $file->store('blog_images');
            }
        }

        $formData['slug'] = str_slug($formData['title']);

        $blog = Blog::create($formData);

        if ($blog->save()) {
            return redirect('/admin/manage-blogs')->with('success', 'Blog added successfully.');
        } else {
            return redirect('/admin/manage-blogs')->with('error', 'Sorry there is an error while adding blog. please try again.');
        }
    }

    public function editBlog($id)
    {
        $row = Blog::find($id);
        return view('admin.blogs.edit', compact('row'));
    }

    public function updateBlog($id, Request $request)
    {
        $formData = request()->except(['_token']);

        $this->validate($request, [
            'title' => ['required', 'string', 'max:255'],
            'description' => ['required', 'string'],
        ], [
            "title.required" => "Please enter :attribute",
            "description.required" => "Please enter :attribute",
        ]);

        if ($request->hasFile('image')) {
            $allowedfileExtension = ['jpeg', 'jpg', 'png', 'svg'];
            $file = $request->file('image');

            $extension = $file->getClientOriginalExtension();

            if (in_array($extension, $allowedfileExtension)) {

                $formData['image'] = $file->store('blog_images');
            }
        }

        $formData['slug'] = str_slug($formData['title']);

        // save the row to the database
        $duplicateEntry = Blog::whereTitle($formData['title'])->first();

        if ($duplicateEntry == '' || ($duplicateEntry != '' && $duplicateEntry->id == $id)) {
            Blog::find($id)->update($formData);
        } else
            return redirect()->back()->with('error', 'Blog title already taken.');

        return redirect('/admin/manage-blogs')->with('success', 'Blog updated successfully.');
    }

    public function deleteBlog(Request $request)
    {
        $id = $request->input('id');

        if (Blog::find($id)->delete()) {
            return response()->json(['status' => 'success', 'msg' => 'You have successfully deleted blog']);
        } else {
            return response()->json(['status' => 'error', 'msg' => 'Sorry there is an error in deleting blog. Please try again later!']);
        }
    }

    public function activateDeactivateBlog(Request $request)
    {
        $id = $request->id;
        $status = $request->status;

        if ($status == 1)
            $msg = 'You have successfully activated the blog.';
        else
            $msg = 'You have successfully deactivated the blog.';

        if (Blog::find($id)->update(['status' => $status])) {
            return response()->json(['status' => 'success', 'msg' => $msg]);
        } else {
            return response()->json(['status' => 'error', 'msg' => 'Sorry there is an error while changing the status of blog. Please try again later!']);
        }
    }

    public function manageFaqTopics()
    {
        $result = FaqTopic::all();
        return view('admin.faq-topics.manage', compact('result'));
    }

    public function addFaqTopic()
    {
        return view('admin.faq-topics.add');
    }

    public function saveFaqTopic(Request $request)
    {
        $formData = request()->except(['_token']);

        $this->validate($request, [
            'topic' => ['required', 'string'],
        ], [
            "topic.required" => "Please enter :attribute",
        ]);

        if (isset($formData['lang']) &&  $formData['lang'] != '') {
            $duplicateEntry = FaqTopic::whereTopic($formData['topic'])->where('lang', $formData['lang'])->count();
        } else {
            $duplicateEntry = FaqTopic::whereTopic($formData['topic'])->where('lang', null)->count();
        }

        if ($duplicateEntry == 0) {
            $faq_topic = FaqTopic::create($formData);
            if ($faq_topic->save()) {
                return redirect('/admin/manage-faq-topics')->with('success', 'FAQ Topic added successfully.');
            } else {
                return redirect('/admin/manage-faq-topics')->with('error', 'Sorry there is an error while adding FAQ Topic please try again.');
            }
        } else {
            return redirect('/admin/manage-faq-topics')->with('success', 'Sorry this FAQ Topic already exist.');
        }
    }

    public function editFaqTopic($id)
    {
        $row = FaqTopic::find($id);
        return view('admin.faq-topics.edit', compact('row'));
    }

    public function updateFaqTopic($id, Request $request)
    {
        $formData = request()->except(['_token']);

        $this->validate($request, [
            'topic' => ['required', 'string'],
        ], [
            "topic.required" => "Please enter :attribute",
        ]);

        // $duplicateEntry = FaqTopic::whereTopic($formData['topic'])->count();

        // if ($duplicateEntry == 0) {
        FaqTopic::find($id)->update($formData);
        return redirect('/admin/manage-faq-topics')->with('success', 'FAQ Topic updated successfully.');
        // } else {
        //     return redirect('/admin/manage-faq-topics')->with('success', 'Sorry this FAQ Topic already exist.');
        // }

    }

    public function deleteFaqTopic(Request $request)
    {
        $id = $request->input('id');

        if (FaqTopic::find($id)->delete()) {
            return response()->json(['status' => 'success', 'msg' => 'You have successfully deleted FAQ Topic']);
        } else {
            return response()->json(['status' => 'error', 'msg' => 'Sorry there is an error in deleting FAQ Topic. Please try again later!']);
        }
    }

    public function manageFaqs()
    {
        $result = Faq::with('topic')->get();
        return view('admin.faqs.manage', compact('result'));
    }

    public function addFaq()
    {
        $topics = FaqTopic::all();
        return view('admin.faqs.add', compact('topics'));
    }

    public function saveFaq(Request $request)
    {
        $formData = request()->except(['_token']);

        $this->validate($request, [
            'question' => ['required', 'string'],
            'answer' => ['required', 'string'],
        ], [
            "question.required" => "Please enter :attribute",
            "answer.required" => "Please enter :attribute",
        ]);

        $duplicateEntry = Faq::whereQuestion($formData['question'])->count();

        if ($duplicateEntry == 0) {
            $faq = Faq::create($formData);
            if ($faq->save()) {
                return redirect('/admin/manage-faqs')->with('success', 'FAQ added successfully.');
            } else {
                return redirect('/admin/manage-faqs')->with('error', 'Sorry there is an error while adding FAQ please try again.');
            }
        } else {
            return redirect('/admin/manage-faqs')->with('success', 'Sorry this FAQ already exist.');
        }
    }

    public function editFaq($id)
    {
        $topics = FaqTopic::all();
        $row = Faq::find($id);
        return view('admin.faqs.edit', compact('row', 'topics'));
    }

    public function updateFaq($id, Request $request)
    {
        $formData = request()->except(['_token']);

        $this->validate($request, [
            'question' => ['required', 'string'],
            'answer' => ['required', 'string'],
        ], [
            "question.required" => "Please enter :attribute",
            "answer.required" => "Please enter :attribute",
        ]);

        // save the row to the database
        Faq::find($id)->update($formData);

        return redirect('/admin/manage-faqs')->with('success', 'FAQ updated successfully.');
    }

    public function deleteFaq(Request $request)
    {
        $id = $request->input('id');

        if (Faq::find($id)->delete()) {
            return response()->json(['status' => 'success', 'msg' => 'You have successfully deleted FAQ']);
        } else {
            return response()->json(['status' => 'error', 'msg' => 'Sorry there is an error in deleting FAQ. Please try again later!']);
        }
    }

    public function activateDeactivateFaq(Request $request)
    {
        $id = $request->id;
        $status = $request->status;

        if ($status == 1)
            $msg = 'You have successfully activated the FAQ.';
        else
            $msg = 'You have successfully deactivated the FAQ.';

        if (Faq::find($id)->update(['status' => $status])) {
            return response()->json(['status' => 'success', 'msg' => $msg]);
        } else {
            return response()->json(['status' => 'error', 'msg' => 'Sorry there is an error while changing the status of FAQ. Please try again later!']);
        }
    }

    public function managePages()
    {
        $result = CmsPage::all();
        return view('admin.pages.manage', compact('result'));
    }

    public function addPage()
    {
        return view('admin.pages.add');
    }

    public function savePage(Request $request)
    {
        $formData = request()->except(['_token']);

        $this->validate($request, [
            'title' => ['required', 'string', 'max:255'],
            'content' => ['required', 'string'],
        ], [
            "title.required" => "Please enter :attribute",
            "content.required" => "Please enter :attribute",
        ]);

        if ($request->hasFile('banner')) {
            $allowedfileExtension = ['jpeg', 'jpg', 'png', 'svg', 'JPG', 'PNG', 'JPEG', 'SVG'];
            $file = $request->file('banner');

            $extension = $file->getClientOriginalExtension();

            if (in_array($extension, $allowedfileExtension)) {

                $formData['banner'] = $file->store('page_banners');
            }
        }

        $formData['slug'] = str_slug($formData['title']);
        //$formData['position'] = implode(',', $formData['position']);

        $page = CmsPage::create($formData);

        if ($page->save()) {
            return redirect('/admin/manage-pages')->with('success', 'Page added successfully.');
        } else {
            return redirect('/admin/manage-pages')->with('error', 'Sorry there is an error while adding page. please try again.');
        }
    }

    public function editPage($id)
    {
        $row = CmsPage::find($id);
        return view('admin.pages.edit', compact('row'));
    }

    public function updatePage($id, Request $request)
    {
        $formData = request()->except(['_token']);

        $this->validate($request, [
            'title' => ['required', 'string', 'max:255'],
            'content' => ['required', 'string'],
        ], [
            "title.required" => "Please enter :attribute",
            "content.required" => "Please enter :attribute",
        ]);

        if ($request->hasFile('banner')) {
            $allowedfileExtension = ['jpeg', 'jpg', 'png', 'svg', 'JPG', 'PNG', 'JPEG', 'SVG'];
            $file = $request->file('banner');

            $extension = $file->getClientOriginalExtension();

            if (in_array($extension, $allowedfileExtension)) {

                $formData['banner'] = $file->store('page_banners');
            }
        }

        // $formData['slug'] = str_slug($formData['title']);
        //$formData['position'] = implode(',', $formData['position']);

        // save the row to the database
        $duplicateEntry = CmsPage::whereTitle($formData['title'])->first();

        if ($duplicateEntry == '' || ($duplicateEntry != '' && $duplicateEntry->id == $id)) {
            CmsPage::find($id)->update($formData);
        } else
            return redirect()->back()->with('error', 'Page title already taken.');

        return redirect('/admin/manage-pages')->with('success', 'Page updated successfully.');
    }

    public function deletePage(Request $request)
    {
        $id = $request->input('id');

        if (CmsPage::find($id)->delete()) {
            return response()->json(['status' => 'success', 'msg' => 'You have successfully deleted page']);
        } else {
            return response()->json(['status' => 'error', 'msg' => 'Sorry there is an error in deleting page. Please try again later!']);
        }
    }

    public function activateDeactivatePage(Request $request)
    {
        $id = $request->id;
        $status = $request->status;

        if ($status == 1)
            $msg = 'You have successfully activated the page.';
        else
            $msg = 'You have successfully deactivated the page.';

        if (CmsPage::find($id)->update(['status' => $status])) {
            return response()->json(['status' => 'success', 'msg' => $msg]);
        } else {
            return response()->json(['status' => 'error', 'msg' => 'Sorry there is an error while changing the status of page. Please try again later!']);
        }
    }

    public function notifications()
    {
        DB::table('notifications')->where('read_at', NULL)->where('notifiable_id', Auth::id())->update(['read_at' => Date('Y-m-d H:i:s')]);

        $all_notifications = Auth::user()->notifications()->get();
        $notifications = [];
        foreach ($all_notifications as $notification) {
            $notifications[] = $notification;
        }

        return view('admin.settings.notifications', compact('notifications'));
    }








    public function manageCategory()
    {
        $result = Category::get();
        return view('admin.category.manage', compact('result'));
    }

    public function addCategory()
    {
        return view('admin.category.add');
    }

    public function saveCategory(Request $request)
    {
        $formData = request()->except(['_token']);

        $this->validate($request, [
            'name' => ['required', 'string', 'max:255'],

        ], [
            "name.required" => "Please enter :attribute",
        ]);


        $category = Category::create($formData);


        if ($category->save()) {
            return redirect('/admin/manage-category')->with('success', 'Category added successfully.');
        } else {
            return redirect('/admin/manage-category')->with('error', 'Sorry there is an error while adding category. please try again.');
        }
    }

    public function editCategory($id)
    {
        $row = Category::find($id);
        return view('admin.category.edit', compact('row'));
    }

    public function updateCategory($id, Request $request)
    {
        $formData = request()->except(['_token']);

        $this->validate($request, [
            'name' => ['required', 'string', 'max:255'],

        ], [
            "name.required" => "Please enter :attribute",
        ]);

        // save the row to the database
        $duplicateEntry = Category::whereName($formData['name'])->first();

        if ($duplicateEntry == '' || ($duplicateEntry != '' && $duplicateEntry->id == $id)) {

            Category::find($id)->update($formData);
        } else
            return redirect()->back()->with('error', 'Category already taken.');

        return redirect('/admin/manage-category')->with('success', 'Category updated successfully.');
    }

    public function deleteCategory(Request $request)
    {
        $id = $request->input('id');

        if (Category::find($id)->delete()) {
            return response()->json(['status' => 'success', 'msg' => 'You have successfully deleted category']);
        } else {
            return response()->json(['status' => 'error', 'msg' => 'Sorry there is an error in deleting category. Please try again later!']);
        }
    }





    public function manageSocial()
    {
        $result = SocialKeys::first();
        return view('admin.social.manage', compact('result'));
    }
    public function saveSocial(Request $request)
    {
        $formData = request()->except(['_token']);

        SocialKeys::first()->delete();


        $social = SocialKeys::create($formData);


        if ($social->save()) {
            return redirect('/admin/manage-social')->with('success', 'Keys added successfully.');
        } else {
            return redirect('/admin/manage-social')->with('error', 'Sorry there is an error while adding keys. please try again.');
        }
    }

    public function fetchCities(Request $request)
    {
        $cities = DB::table('cities')->where('state_id', $request->state)->get();
        $data = '<option value="">Select City</option>';
        foreach ($cities as $key => $city) {
            if ($city->name != '') {
                $data .= "<option value=$city->id>$city->name</option>";
            }
        }
        return $data;
    }


    public function manageModes()
    {
        $result = Mode::first();
        return view('admin.modes.manage', compact('result'));
    }
    public function saveModes(Request $request)
    {
        $formData = request()->except(['_token']);

        $result = Mode::first();
        if ($result) {
            Mode::first()->delete();
        }

        $social = Mode::create($formData);

        if ($social->save()) {
            return redirect('/admin/manage-modes')->with('success', 'Values added successfully.');
        } else {
            return redirect('/admin/manage-modes')->with('error', 'Sorry there is an error while adding Values. please try again.');
        }
    }

    public function makeLive(Request $request)
    {
        $status = $request->status;

        if ($status == 1)
            $msg = 'You have successfully activated the Production Mode.';
        else
            $msg = 'You have successfully  activated the Testing Mode.';

        if (Mode::first()->update(['status' => $status])) {
            return response()->json(['status' => 'success', 'msg' => $msg]);
        } else {
            return response()->json(['status' => 'error', 'msg' => 'Sorry there is an error while changing the status . Please try again later!']);
        }
    }



    public function manageDialogue()
    {
        $result = Dialogue::get();
        return view('admin.dialogue.manage', compact('result'));
    }

    public function editDialogue($id)
    {
        $row = Dialogue::find($id);
        return view('admin.dialogue.edit', compact('row'));
    }

    public function updateDialogue($id, Request $request)
    {
        $formData = request()->except(['_token']);

        $this->validate($request, [
            'name' => ['required', 'string', 'max:255'],

        ], [
            "name.required" => "Please enter :attribute",
        ]);

        // save the row to the database
        $duplicateEntry = Dialogue::whereName($formData['name'])->first();

        if ($duplicateEntry == '' || ($duplicateEntry != '' && $duplicateEntry->id == $id)) {

            Dialogue::find($id)->update($formData);
        } else
            return redirect()->back()->with('error', 'Dialogue already taken.');

        return redirect('/admin/manage-dialogue')->with('success', 'Dialogue updated successfully.');
    }

    public function deleteDialogue(Request $request)
    {
        $id = $request->input('id');

        if (Dialogue::find($id)->delete()) {
            return response()->json(['status' => 'success', 'msg' => 'You have successfully deleted dialogue']);
        } else {
            return response()->json(['status' => 'error', 'msg' => 'Sorry there is an error in deleting dialogue. Please try again later!']);
        }
    }



    public function manageCampaignTime()
    {
        $result = CampaignRequestTime::first();
        return view('admin.campaign.manage', compact('result'));
    }
    public function saveCampaignTime(Request $request)
    {
        $formData = request()->except(['_token']);

        CampaignRequestTime::first()->delete();


        $social = CampaignRequestTime::create($formData);


        if ($social->save()) {
            return redirect('/admin/manage-campaign-time')->with('success', 'Time added successfully.');
        } else {
            return redirect('/admin/manage-campaign-time')->with('error', 'Sorry there is an error while adding time. please try again.');
        }
    }

    public function manageComplaint()
    {
        $result = Complaint::orderBy('id', 'desc')->get();
        return view('admin.complaint.manage', compact('result'));
    }
    public function managePausedCampaigns()
    {
        $result = PausedCampaign::orderBy('id', 'desc')->with('influencer_request_details')->get();
        return view('admin.campaigns.paused_campaigns', compact('result'));
    }
    public function updateComplaintStatus($rowId, $status)
    {
        $complaint                        = Complaint::whereId($rowId)->with('influencer_request_accepts.influencer_request_details')->first();
        $id                               = $complaint->influencer_request_accepts->influencer_request_details->id;
        $influencerDetail                = InfluencerRequestDetail::where('id', $id)->first();
        if ($status == "Confirmed") {
            // cancel campaign for influencer
            $row = InfluencerRequestDetail::leftjoin('influencer_request_accepts',  'influencer_request_accepts.influencer_request_detail_id', '=', 'influencer_request_details.id')
                ->select('influencer_request_details.*', 'influencer_request_accepts.request', 'influencer_request_accepts.request_time', 'influencer_request_accepts.request_time_accept')
                ->where('influencer_request_details.id', $influencerDetail->id)
                ->first();

            $amount = 0;
            $fieldName = $row->advertising . '_price';
            $user =  User::where('id', $row->influencerdetails->user_id)->first();
            if ($user->advertisingMethodPrice != null) {
                $amount = $user->advertisingMethodPrice->$fieldName;
            }
            // $stripe_account = StripeAccount::where('user_id',$row->user_id)->first();

            $customer =  User::where('id', $row->user_id)->first();
            $txn_id = $row->invoices->charge_id ?? '';

            // Use the new centralized cancelCampaign method for dispute handling
            $result = $row->cancelCampaign('dispute_confirmed', [
                'process_refund' => true,
                'adjust_price' => true,
                'remove_pause' => true,
                'send_notifications' => false, // We'll send custom notifications below
                'metadata' => [
                    'dispute_id' => $request->dispute_id ?? null,
                    'admin_action' => 'dispute_confirmed',
                    'controller' => 'AdminController',
                    'method' => 'disputeConfirm'
                ]
            ]);

            if ($result['success']) {
                // Send custom notifications for dispute confirmation
                dispatch(new NewCancelRefund($customer, $row));
                $customer->notify(new CancelRefund($customer, $row));

                \Log::info('Dispute confirmed and processed successfully via cancelCampaign', [
                    'influencer_request_detail_id' => $row->id,
                    'campaign_id' => $row->compaign_id,
                    'refund_processed' => $result['refund_processed'],
                    'refund_id' => $result['refund_id'] ?? null,
                    'price_adjusted' => $result['price_adjusted'],
                    'price_adjustment_amount' => $result['price_adjustment_amount'] ?? null
                ]);
            } else {
                \Log::error('Dispute confirmation failed via cancelCampaign', [
                    'influencer_request_detail_id' => $row->id,
                    'campaign_id' => $row->compaign_id,
                    'error_code' => $result['error_code'] ?? null,
                    'error_message' => $result['message'] ?? null,
                    'error_details' => $result['error_details'] ?? null
                ]);
            }
        } else if ($status == "Cancelled") {
            $id = $complaint->influencer_request_accepts->influencer_request_details->id;
            $row = InfluencerRequestDetail::leftjoin('influencer_request_accepts', 'influencer_request_accepts.influencer_request_detail_id', '=', 'influencer_request_details.id')
                ->select('influencer_request_details.*', 'influencer_request_accepts.request', 'influencer_request_accepts.request_time', 'influencer_request_accepts.request_time_accept')
                ->where('influencer_request_details.id', $id)
                ->first();


            $row->review   = 1;
            $row->save();

            $posts = SocialPost::where('user_id', $row->influencerdetails->user_id)->where('media', $row->media)->get();

            foreach ($posts as $post) {

                $influencer = InfluencerRequestDetail::where('social_post_id', $post->id)->first();

                if ($influencer == '') {

                    SocialPost::where('id', $post->id)->delete();
                }
            }
        }
        // if paused let remove
        if ($influencerDetail->is_paused == 1) {
            $influencerDetail->is_paused = 0;
            $influencerDetail->save();
        }
        // update complaint status
        $complaint->status                = $status;
        $complaint->save();

        return response()->json([
            'status'    => 200,
            'msg'       => 'Complaint status updated successfully'
        ]);
    }

    /**
     * Process and save complaint status updates for multiple complaints
     *
     * This method handles the bulk processing of complaint status changes, including:
     * - Confirming disputes and processing refunds through the centralized cancelCampaign system
     * - Rejecting complaints and restoring campaign status
     * - Managing points deduction for influencers when disputes are confirmed
     * - Sending appropriate notifications to all parties involved
     *
     * @param Request $request The HTTP request containing complaint data
     *                        Expected format:
     *                        - ids[]: Array of complaint IDs to process
     *                        - status[]: Array of corresponding status values ('Confirmed' or 'Rejected')
     *
     * @return \Illuminate\Http\RedirectResponse Redirects to manage-complaints page with success message
     *
     * @throws \Exception When complaint processing fails or required relationships are missing
     *
     * @since 1.0.0
     * <AUTHOR> System
     *
     * @example
     * POST /admin/manage-complaints
     * {
     *     "ids": ["123", "456"],
     *     "status": ["Confirmed", "Rejected"]
     * }
     *
     * Business Logic Flow:
     * 1. Validates complaint hasn't already been processed with the same status
     * 2. For 'Confirmed' complaints:
     *    - Uses cancelCampaign('dispute_confirmed') for refund processing
     *    - Deducts points from influencer via AdminGamification rules
     *    - Sends notifications to customer and influencer
     * 3. For 'Rejected' complaints:
     *    - Restores campaign to pending status
     *    - Clears refund reason and sets completion date
     *    - Sends rejection notifications
     *
     * Database Changes:
     * - Updates complaint status in complaints table
     * - Modifies campaign payment_status, refund_reason, and related fields
     * - Creates statistics entries for point deductions
     * - Updates invoice refund status when applicable
     *
     * Stripe Integration:
     * - Uses modern separate charges and transfer refund system
     * - Processes refunds through centralized cancelCampaign method
     * - Includes comprehensive metadata for audit trails
     *
     * Error Handling:
     * - Logs successful and failed operations with detailed context
     * - Continues processing other complaints even if one fails
     * - Maintains data consistency through proper transaction handling
     */
    public function saveComplaint(Request $request)
    {
        $formData = request()->except(['_token']);

        for ($i = 0; $i < count($formData['ids']); $i++) {
            $checkcomplaint = Complaint::join('influencer_request_accepts', 'influencer_request_accepts.id', '=', 'complaints.influencer_request_accept_id')
                ->select('complaints.*')
                ->where('complaints.id', $formData['ids'][$i])
                ->where('complaints.status', $formData['status'][$i])
                ->first();

            if ($checkcomplaint == '') {
                $complaint = Complaint::leftjoin('influencer_request_accepts', 'influencer_request_accepts.id', '=', 'complaints.influencer_request_accept_id')
                    ->select('influencer_request_accepts.*', 'complaints.status')
                    ->where('complaints.id', $formData['ids'][$i])
                    ->first();

                $influencerRequestDetail = InfluencerRequestDetail::leftjoin('influencer_request_accepts',  'influencer_request_accepts.influencer_request_detail_id', '=', 'influencer_request_details.id')
                    ->select('influencer_request_details.*', 'influencer_request_accepts.request', 'influencer_request_accepts.request_time', 'influencer_request_accepts.request_time_accept')
                    ->where('influencer_request_details.id', $complaint->influencer_request_detail_id)
                    ->first();

                $user =  User::where('id', $influencerRequestDetail->influencerdetails->user_id)->first();
                if ($formData['status'][$i] == 'Confirmed'  && $complaint->status != 'Confirmed') {
                    $amount = $influencerRequestDetail->current_price;
                    $fieldName = $influencerRequestDetail->advertising . '_price';

                    $customer =  User::where('id', $influencerRequestDetail->user_id)->first();

                    $txn_id = $influencerRequestDetail->invoices->charge_id ?? '';

                    // Use the new centralized cancelCampaign method for complaint confirmation
                    $result = $influencerRequestDetail->cancelCampaign('dispute_confirmed', [
                        'process_refund' => true,
                        'adjust_price' => true,
                        'remove_pause' => true,
                        'send_notifications' => false, // We'll send custom notifications below
                        'metadata' => [
                            'complaint_id' => $formData['ids'][$i],
                            'admin_action' => 'complaint_confirmed',
                            'controller' => 'AdminController',
                            'method' => 'saveComplaint'
                        ]
                    ]);

                    if ($result['success']) {
                        \Log::info('Complaint confirmed and processed successfully via cancelCampaign', [
                            'complaint_id' => $formData['ids'][$i],
                            'influencer_request_detail_id' => $influencerRequestDetail->id,
                            'campaign_id' => $influencerRequestDetail->compaign_id,
                            'refund_processed' => $result['refund_processed'],
                            'refund_id' => $result['refund_id'] ?? null,
                            'price_adjusted' => $result['price_adjusted'],
                            'price_adjustment_amount' => $result['price_adjustment_amount'] ?? null
                        ]);
                    } else {
                        \Log::error('Complaint confirmation failed via cancelCampaign', [
                            'complaint_id' => $formData['ids'][$i],
                            'influencer_request_detail_id' => $influencerRequestDetail->id,
                            'campaign_id' => $influencerRequestDetail->compaign_id,
                            'error_code' => $result['error_code'] ?? null,
                            'error_message' => $result['message'] ?? null,
                            'error_details' => $result['error_details'] ?? null
                        ]);
                    }

                    // Update complaint status
                    Complaint::find($formData['ids'][$i])->update(['status' => $formData['status'][$i]]);

                    // Handle points deduction and notifications for successful complaint confirmation
                    if ($result['success']) {
                        $results = AdminGamification::where('select_type', 'Point-Rules')->first();

                        // Deduct points from influencer for confirmed dispute
                        Statistic::create([
                            'user_id' => $influencerRequestDetail->influencerdetails->user_id,
                            'points' => $results->points_disputes,
                            'type' => '0',
                            'title'   => '[' . $influencerRequestDetail->compaign_id . ']</br>' .
                                $results->points_disputes . ' points lost because ' .
                                $influencerRequestDetail->user->first_name . ' disputed the campaign successfully',
                            'date' => date('Y-m-d H:i:s'),
                        ]);

                        // Send notifications for complaint confirmation
                        dispatch(new NewComplaintUpdate($customer, $influencerRequestDetail));
                        $customer->notify(new ComplaintUpdate($customer, $influencerRequestDetail));

                        dispatch(new NewcustomerComplaintwasAccepted($user, $influencerRequestDetail));
                    }
                } else {
                    $influencerRequestDetail->update([
                        'review' => '1',
                        'refund_reason' => null,
                        'completed_at' => now(),
                        'payment_status' => InfluencerRequestDetail::STATUS_PENDING
                    ]);

                    Complaint::find($formData['ids'][$i])->update(['status' => $formData['status'][$i]]);
                    $complaint = Complaint::find($formData['ids'][$i]);

                    $customer = User::whereId($complaint->user_id)->first();

                    dispatch(new NewcomplaintCancelled($customer, $influencerRequestDetail));
                    $customer->notify(new complaintCancelled($customer, $influencerRequestDetail));

                    dispatch(new NewcustomerComplaintwasRejected($user, $influencerRequestDetail));
                }
            }
        }

        return redirect('/admin/manage-complaints')->with('success', 'Status updated successfully!');
    }

    public function manageComission()
    {
        $result = AdminComission::first();
        return view('admin.comission.manage', compact('result'));
    }
    
    public function saveComission(Request $request)
    {
        $formData = request()->except(['_token']);

        AdminComission::first()->delete();
        AdminComission::create($formData);

        return redirect('/admin/manage-comission')->with('success', 'Comission updated successfully!');
    }

    public function manageHashtag()
    {
        $result = AdminHashtag::first();
        return view('admin.manage-hashtag', compact('result'));
    }

    public function saveHashtag(Request $request)
    {
        $formData = request()->except(['_token']);

        AdminHashtag::first()->delete();
        AdminHashtag::create($formData);

        return redirect('/admin/manage-hashtag')->with('success', 'Hashtag updated successfully!');
    }

    public function manageDispute()
    {
        $result = ContactSupport::get();
        return view('admin.disputes.manage', compact('result'));
    }

    public function managePricing()
    {
        $result = AdminPricing::groupBy('media', 'type')->get();
        return view('admin.pricing.manage', compact('result'));
    }

    public function addPricing()
    {
        $countries = Country::get();
        return view('admin.pricing.add', compact('countries'));
    }

    public function savePricing(Request $request)
    {
        $formData = request()->except(['_token']);
        $i = 0;

        foreach ($formData['country'] as $country) {
            AdminPricing::create([
                'type' => $formData['type'],
                'media' => $formData['media'],
                'country' => $formData['country'][$i],
                'range' => $formData['range'][$i],
                'cpt' => $formData['cpt'][$i],
            ]);

            $i++;
        }

        return redirect('/admin/manage-pricing')->with('success', 'Pricing added successfully!');
    }

    public function editPricing($id)
    {
        $result_id = AdminPricing::where('id', $id)->first();
        $countries = Country::get();
        $result = AdminPricing::where('media', $result_id->media)->where('type', $result_id->type)->get();
        return view('admin.pricing.edit', compact('result', 'countries'));
    }

    public function updatePricing($id, Request $request)
    {
        $formData = request()->except(['_token']);

        $result_id = AdminPricing::where('id', $id)->first();

        if (AdminPricing::where('media', $result_id->media)->where('type', $result_id->type)->get() != '') {
            AdminPricing::where('media', $result_id->media)->where('type', $result_id->type)->delete();
        }

        $i = 0;
        
        foreach ($formData['country'] as $country) {
            AdminPricing::create([
                'type' => $formData['type'],
                'media' => $formData['media'],
                'country' => $formData['country'][$i],
                'range' => $formData['range'][$i],
                'cpt' => $formData['cpt'][$i],
            ]);

            $i++;
        }

        return redirect('/admin/manage-pricing')->with('success', 'Pricing updated successfully.');
    }

    public function deletePricing(Request $request)
    {
        $id = $request->input('id');

        $result_id = AdminPricing::where('id', $id)->first();

        if (AdminPricing::where('media', $result_id->media)->where('type', $result_id->type)->delete()) {
            return response()->json(['status' => 'success', 'msg' => 'You have successfully deleted pricing']);
        } else {
            return response()->json(['status' => 'error', 'msg' => 'Sorry there is an error in deleting pricing. Please try again later!']);
        }
    }

    public function manageGamification()
    {
        $result = AdminGamification::groupBy('select_type')->get();
        return view('admin.gamification.manage', compact('result'));
    }

    public function addGamification()
    {
        return view('admin.gamification.add');
    }

    public function saveGamification(Request $request)
    {
        $formData = request()->except(['_token']);

        if (AdminGamification::where('select_type', $formData['select_type'])->get() != '' && $formData['select_type'] == "Pricing & Rank") {
            AdminGamification::where('select_type', $formData['select_type'])->delete();
        }
        // dd($formData);
        if ($formData['select_type'] == "Pricing & Rank") {
            $i = 0;
            foreach ($formData['type'] as $type) {

                AdminGamification::create([
                    'type' => $type,
                    'select_type' => $formData['select_type'],
                    'pricing' => $formData['pricing'][$i],
                    'requirement' => $formData['requirement'][$i],
                ]);
                $i++;
            }
        }


        if ($formData['select_type'] == "Point-Rules") {
            $result = AdminGamification::where('select_type', $formData['select_type'])->first();

            if ($result == '') {
                AdminGamification::create([
                    'select_type' => $formData['select_type'],
                    "points_five_star" => $formData['points_five_star'],
                    "points_four_star" => $formData['points_four_star'],
                    "points_completed_time" => $formData['points_completed_time'],
                    "quick_response" => $formData['quick_response'],
                    "repeat_bookings" => $formData['repeat_bookings'],
                    "daily_login" => $formData['daily_login'],
                    "successfull_campaign" => $formData['successfull_campaign'],
                    "points_one_star" => $formData['points_one_star'],
                    "points_two_star" => $formData['points_two_star'],
                    "points_deadlines" => $formData['points_deadlines'],
                    "points_disputes" => $formData['points_disputes'],
                    "points_half_time" => $formData['points_half_time']
                ]);
            } else {
                $result->update([
                    "points_five_star" => $formData['points_five_star'],
                    "points_four_star" => $formData['points_four_star'],
                    "points_completed_time" => $formData['points_completed_time'],
                    "quick_response" => $formData['quick_response'],
                    "repeat_bookings" => $formData['repeat_bookings'],
                    "daily_login" => $formData['daily_login'],
                    "successfull_campaign" => $formData['successfull_campaign'],
                    "points_one_star" => $formData['points_one_star'],
                    "points_two_star" => $formData['points_two_star'],
                    "points_deadlines" => $formData['points_deadlines'],
                    "points_disputes" => $formData['points_disputes'],
                    "points_half_time" => $formData['points_half_time']
                ]);
            }
        }
        return redirect('/admin/manage-gamification')->with('success', 'Gamification updated successfully!');
    }
    public function editGamification($id)
    {
        $result  = AdminGamification::where('select_type', $id)->get();
        return view('admin.gamification.edit', compact('result'));
    }
    public function updateGamification($id, Request $request)
    {
        $formData = request()->except(['_token']);

        $result_id = AdminGamification::where('select_type', $id)->first();

        if (AdminGamification::where('select_type', $formData['select_type'])->get() != '' && $formData['select_type'] == "Pricing & Rank") {
            AdminGamification::where('select_type', $formData['select_type'])->delete();
        };
        if ($formData['select_type'] == "Pricing & Rank") {
            $i = 0;
            foreach ($formData['type'] as $type) {

                AdminGamification::create([
                    'type' => $type,
                    'select_type' => $formData['select_type'],
                    'pricing' => $formData['pricing'][$i],
                    'requirement' => $formData['requirement'][$i],
                ]);
                $i++;
            }
        }


        if ($formData['select_type'] == "Point-Rules") {
            $result = AdminGamification::where('select_type', $formData['select_type'])->first();

            if ($result == '') {
                AdminGamification::create([
                    'select_type' => $formData['select_type'],
                    "points_five_star" => $formData['points_five_star'],
                    "points_four_star" => $formData['points_four_star'],
                    "points_completed_time" => $formData['points_completed_time'],
                    "quick_response" => $formData['quick_response'],
                    "repeat_bookings" => $formData['repeat_bookings'],
                    // "daily_login" => $formData['daily_login'],
                    // "successfull_campaign" => $formData['successfull_campaign'],
                    "points_one_star" => $formData['points_one_star'],
                    "points_two_star" => $formData['points_two_star'],
                    "points_deadlines" => $formData['points_deadlines'],
                    "points_disputes" => $formData['points_disputes'],
                    "points_half_time" => $formData['points_half_time']
                ]);
            } else {
                $result->update([
                    "points_five_star" => $formData['points_five_star'],
                    "points_four_star" => $formData['points_four_star'],
                    "points_completed_time" => $formData['points_completed_time'],
                    "quick_response" => $formData['quick_response'],
                    "repeat_bookings" => $formData['repeat_bookings'],
                    // "daily_login" => $formData['daily_login'],
                    // "successfull_campaign" => $formData['successfull_campaign'],
                    "points_one_star" => $formData['points_one_star'],
                    "points_two_star" => $formData['points_two_star'],
                    "points_deadlines" => $formData['points_deadlines'],
                    "points_disputes" => $formData['points_disputes'],
                    "points_half_time" => $formData['points_half_time']
                ]);
            }
        }

        return redirect('/admin/manage-gamification')->with('success', ' Gamification updated successfully.');
    }


    public function manageCampaign()
    {
        $result = InfluencerRequestDetail::groupBy('compaign_id')->get();
        foreach ($result as $row) {
            $row->tasks  = RequestTask::where('influencer_request_detail_id', $row->compaign_id)->get();
        }
        return view('admin.campaigns.manage', compact('result'));
    }
    public function saveCampaign(Request $request)
    {
        $formData = request()->except(['_token']);
        // dd($formData);
        if ($formData != null &&  isset(($formData['ids']))) {
            for ($i = 0; $i < count($formData['ids']); $i++) {

                $detail  = InfluencerRequestDetail::where('id', $formData['ids'][$i])->first();
                if (isset($detail) && $detail->status != 'Cancelled') {
                    // Use the new centralized cancelCampaign method
                    $result = $detail->cancelCampaign('cancelled_by_admin', [
                        'process_refund' => true,
                        'adjust_price' => true,
                        'remove_pause' => true,
                        'send_notifications' => false, // We'll send custom notifications below
                        'metadata' => [
                            'admin_user_id' => Auth::id(),
                            'admin_action' => 'bulk_cancel',
                            'controller' => 'AdminController',
                            'method' => 'saveCampaign'
                        ]
                    ]);

                    if ($result['success']) {
                        // Send custom notifications for admin cancellation
                        $InfluencerDetail = InfluencerDetail::where('id', $detail->influencer_detail_id)->first();
                        $influencer = User::whereId($InfluencerDetail->user_id)->first();

                        dispatch(new NewRequestCancelInfluencer($influencer, Auth::user(), $detail));
                        $influencer->notify(new RequestCancelInfluencer($influencer, Auth::user(), $detail));

                        \Log::info('Campaign cancelled by admin via cancelCampaign', [
                            'influencer_request_detail_id' => $detail->id,
                            'campaign_id' => $detail->compaign_id,
                            'admin_user_id' => Auth::id(),
                            'refund_processed' => $result['refund_processed'],
                            'refund_id' => $result['refund_id'] ?? null
                        ]);
                    } else {
                        \Log::error('Admin campaign cancellation failed via cancelCampaign', [
                            'influencer_request_detail_id' => $detail->id,
                            'campaign_id' => $detail->compaign_id,
                            'admin_user_id' => Auth::id(),
                            'error_code' => $result['error_code'] ?? null,
                            'error_message' => $result['message'] ?? null
                        ]);
                    }
                }
            }
            return redirect('/admin/manage-campaigns')->with('success', 'Campaigns updated successfully!');
        } else {

            return redirect('/admin/manage-campaigns');
        }
    }

    public function manageTask()
    {
        $taskGroups = Task::groupBy('media', 'type', 'select_type')->get();
        return view('admin.tasks.manage', compact('taskGroups'));
    }

    public function addTask()
    {
        return view('admin.tasks.add');
    }

    public function saveTask(Request $request)
    {
        $formdata = request()->except(['_token']);

        $i = 0;
        foreach ($formdata['task'] as $task) {

            Task::create([
                'media' => $formdata['media'],
                'type' => $formdata['type'],
                'select_type' => $formdata['select_type'],
                'task_type' => $formdata['task_type'][$i],
                'task' => $formdata['task'][$i],
                'input_type' => isset($formdata['input_type'][$i]) ? $formdata['input_type'][$i] : '',
                'input_title' => isset($formdata['input_title'][$i]) ? $formdata['input_title'][$i] : '',
                'input_placeholder' => isset($formdata['input_placeholder'][$i]) ? $formdata['input_placeholder'][$i] : '',
                'count' =>  isset($formdata['input_count'][$i]) ? $formdata['input_count'][$i] : '',
                'tag_count' =>  isset($formdata['hashtag_count'][$i]) ? $formdata['hashtag_count'][$i] : ''

            ]);
            $i++;
        }
        return redirect('/admin/manage-tasks')->with('success', 'Tasks updated successfully!');
    }

    public function editTask($id)
    {
        $mainTask = Task::where('id', $id)->first();
        $tasks = Task::where('media', $mainTask->media)->where('type', $mainTask->type)->where('select_type', $mainTask->select_type)->get();
        return view('admin.tasks.edit', compact('mainTask', 'tasks'));
    }

    public function updateTask($id, Request $request)
    {
        $formdata = request()->except(['_token']);

        $i = 0;
        foreach ($formdata['task'] as $task) {
            if ($formdata['task_id'][$i] != '0') {
                $result = Task::where('id', $formdata['task_id'][$i])->first();
                $result->update([
                    'media' => $formdata['media'],
                    'type' => $formdata['type'],
                    'select_type' => $formdata['select_type'],
                    'task_type' => $formdata['task_type'][$i],
                    'task' => $formdata['task'][$i],
                    'input_type' => isset($formdata['input_type'][$i]) ? $formdata['input_type'][$i] : '',
                    'input_title' => isset($formdata['input_title'][$i]) ? $formdata['input_title'][$i] : '',
                    'input_placeholder' => isset($formdata['input_placeholder'][$i]) ? $formdata['input_placeholder'][$i] : '',
                    'count' =>  isset($formdata['input_count'][$i]) ? $formdata['input_count'][$i] : '',
                    'tag_count' =>  isset($formdata['hashtag_count'][$i]) ? $formdata['hashtag_count'][$i] : ''
                ]);
            } else {
                Task::create([
                    'media' => $formdata['media'],
                    'type' => $formdata['type'],
                    'select_type' => $formdata['select_type'],
                    'task_type' => $formdata['task_type'][$i],
                    'task' => $formdata['task'][$i],
                    'input_type' => isset($formdata['input_type'][$i]) ? $formdata['input_type'][$i] : '',
                    'input_title' => isset($formdata['input_title'][$i]) ? $formdata['input_title'][$i] : '',
                    'input_placeholder' => isset($formdata['input_placeholder'][$i]) ? $formdata['input_placeholder'][$i] : '',
                    'count' =>  isset($formdata['input_count'][$i]) ? $formdata['input_count'][$i] : '',
                    'tag_count' =>  isset($formdata['hashtag_count'][$i]) ? $formdata['hashtag_count'][$i] : ''
                ]);
            }

            $i++;
        }

        return redirect('/admin/manage-tasks')->with('success', 'Task updated successfully.');
    }

    public function deleteTask(Request $request)
    {
        $id = $request->input('id');

        $result_id = Task::where('id', $id)->first();
        if (Task::where('media', $result_id->media)->where('type', $result_id->type)->where('select_type', $result_id->select_type)->delete()) {
            return response()->json(['status' => 'success', 'msg' => 'You have successfully deleted task']);
        } else {
            return response()->json(['status' => 'error', 'msg' => 'Sorry there is an error in deleting task. Please try again later!']);
        }
    }
    public function removeTask($id)
    {
        $task = Task::where('id', $id);
        $task->delete();
        return response()->json([
            'status' => 200,
            'message' => 'Task has been deleted succesfully'
        ]);
    }




    public function setPasswordSend(Request $request)
    {
        $id = $request->id;
        $type = $request->type;
        $user  = User::find($id);

        if ($type == 1) {
            $user->update(['set_password' => 1]);
            $token = str_random(60);
            $password_reset_user = DB::table('password_resets')
                ->where('email', $user->email)
                ->first();
            if ($password_reset_user) {
                $token_saved = DB::table('password_resets')
                    ->where('email', $password_reset_user->email)
                    ->update([
                        'token' => $token
                    ]);
            } else {
                $token_saved = DB::table('password_resets')->insert([
                    'email' => $user->email,
                    'token' => $token,
                    'created_at' => date('Y-m-d H:i:s')
                ]);
            }
            if ($token_saved) {
                dispatch(new NewSendActivateUser($user, $token));
                return response()->json(['status' => 'success', 'msg' => 'Activation mail send to User']);
            } else {
                return back()->with('error', 'This email does not exist.');
            }
        } else {
            $user->update(['set_password' => null, 'password' => null]);
            return response()->json(['status' => 'success', 'msg' => 'User has been deactivated successfully']);
        }
    }


    public function activateUserSend(Request $request)
    {
        $id = $request->id;

        $user  = User::find($id);

        $user->update(['flag' => 1, 'trophy' => 'Bronze']);


        $token = str_random(60);
        // $password_reset_user = DB::table('password_resets')
        //     ->where('email', $user->email)
        //     ->first();
        // if ($password_reset_user) {
        //     $token_saved = DB::table('password_resets')
        //         ->where('email', $password_reset_user->email)
        //         ->update([
        //             'token' => $token]);
        // } else {
        //     $token_saved = DB::table('password_resets')->insert(['email' => $user->email,
        //         'token' => $token, 'created_at' => date('Y-m-d H:i:s')]);
        // }
        // if ($token_saved) {
        // dispatch(new NewSendActivateUser($user,$token));
        dispatch(new NewwelcomeToTheClosedBeta($user, $token));

        // $user->notify(new SendActivateUser($user,$token));
        return response()->json(['status' => 'success', 'msg' => 'Welcome mail send to User']);
        // } else {
        //     return back()->with('error', 'This email does not exist.');
        // }



    }


    public function deactivateUserSend(Request $request)
    {
        $id = $request->id;

        $user  = User::find($id);

        $user->update(['flag' => '']);

        return response()->json(['status' => 'success', 'msg' => 'Deactivation updated for User']);
    }







    public function manageSmCampaign()
    {
        $result = SmCampaign::get();
        return view('admin.campaigns.manage_sm_campaings', compact('result'));
    }
    public function saveSmCampaign(Request $request)
    {
        $formData = request()->except(['_token']);

        SmCampaign::truncate();

        if (isset($formData['media_all'])) {
            foreach ($formData['media_all'] as $media) {
                SmCampaign::create([
                    'media' => $media,
                    'type' => 'influencer',
                    'status' => 1
                ]);
            }
        }


        if (isset($formData['type_boost'])) {
            if (isset($formData['media_boost'])) {
                foreach ($formData['media_boost'] as $media) {
                    SmCampaign::create([
                        'media' => $media,
                        'type' => $formData['type_boost'],
                        'status' => 1
                    ]);
                }
            } else {
                SmCampaign::create([
                    'media' => '',
                    'type' => $formData['type_boost'],
                    'status' => 1
                ]);
            }
        }

        if (isset($formData['type_reaction'])) {
            if (isset($formData['media_reaction'])) {
                foreach ($formData['media_reaction'] as $media) {
                    SmCampaign::create([
                        'media' => $media,
                        'type' => $formData['type_reaction'],
                        'status' => 1
                    ]);
                }
            } else {
                SmCampaign::create([
                    'media' => '',
                    'type' => $formData['type_reaction'],
                    'status' => 1
                ]);
            }
        }

        if (isset($formData['type_survey'])) {
            if (isset($formData['media_survey'])) {
                foreach ($formData['media_survey'] as $media) {
                    SmCampaign::create([
                        'media' => $media,
                        'type' => $formData['type_survey'],
                        'status' => 1
                    ]);
                }
            } else {
                SmCampaign::create([
                    'media' => '',
                    'type' => $formData['type_survey'],
                    'status' => 1
                ]);
            }
        }



        return redirect('/admin/manage-sm-campaign')->with('success', 'SM & Campaigns updated successfully!');
    }


    public function getCampaignList(Request $request)
    {
        $compaign_id = $request->compaign_id;

        $result = InfluencerRequestDetail::where('compaign_id', "like", "%$compaign_id%")->groupBy('compaign_id')->get();

        foreach ($result as $row) {
            $row->tasks  = RequestTask::where('influencer_request_detail_id', $row->compaign_id)->get();
        }

        return response([
            'status' => true,
            'result' => $result,
            'generalPage' => \View::make('admin.campaigns.manage_page', compact('result'))->render(),
        ]);
    }
}
