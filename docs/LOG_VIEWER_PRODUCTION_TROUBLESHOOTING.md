# Log Viewer Production Troubleshooting Guide

## Issue: 403 Forbidden on Production (Works on Staging)

Since the log-viewer works on staging but shows 403 Forbidden on production, this indicates a deployment or configuration issue.

## Step-by-Step Troubleshooting

### 1. Verify Files Are Deployed

Check if all the new files exist on production:

```bash
# Check if AdminOnly middleware exists
ls -la app/Http/Middleware/AdminOnly.php

# Check if Kernel.php has the middleware registered
grep -n "admin.only" app/Http/Kernel.php

# Check if log-viewer config has been updated
grep -A 5 -B 5 "admin.only" config/log-viewer.php
```

### 2. Clear All Caches

Run these commands on production:

```bash
# Clear configuration cache
php artisan config:clear

# Clear application cache
php artisan cache:clear

# Clear route cache
php artisan route:clear

# Clear view cache
php artisan view:clear

# Dump autoloader (important for new middleware)
composer dump-autoload

# Optional: Clear opcache if using PHP opcache
php artisan opcache:clear
```

### 3. Verify Admin Authentication

Test if admin login is working on production:

```bash
# Test admin login route
curl -I https://your-production-domain.com/admin/login
# Should return 200 OK

# Check if you can access admin dashboard after login
# Login via browser and try accessing /admin/
```

### 4. Debug Middleware Registration

Create a temporary test route to verify middleware is working:

```php
// Add this temporarily to routes/web.php for testing
Route::get('/test-admin-middleware', function () {
    return response()->json([
        'message' => 'Admin middleware is working!',
        'user' => auth()->user() ? [
            'id' => auth()->user()->id,
            'user_type' => auth()->user()->user_type,
        ] : null
    ]);
})->middleware(['web', 'admin.only']);
```

Test this route:
- As admin: Should return success message
- As non-admin: Should return 403
- Not logged in: Should return 403

### 5. Check Log Viewer Configuration

Verify the log-viewer config on production:

```bash
# Check current middleware configuration
php artisan config:show log-viewer.middleware

# Should show:
# [
#   "web",
#   "admin.only",
#   "Opcodes\\LogViewer\\Http\\Middleware\\AuthorizeLogViewer"
# ]
```

### 6. Check Laravel Logs

Look for errors in Laravel logs:

```bash
# Check recent logs
tail -f storage/logs/laravel.log

# Look for middleware or authentication errors
grep -i "middleware\|auth\|403" storage/logs/laravel-$(date +%Y-%m-%d).log
```

### 7. Verify Environment Configuration

Check production .env file:

```bash
# Ensure log viewer is enabled
grep LOG_VIEWER .env

# Should have:
# LOG_VIEWER_ENABLED=true
# LOG_VIEWER_API_ONLY=false
```

### 8. Test Direct Middleware Access

Create a simple test to verify the AdminOnly middleware:

```bash
# In tinker
php artisan tinker

# Test the middleware logic
$user = \App\Models\User::where('user_type', 'admin')->first();
echo "Admin user found: " . ($user ? 'Yes' : 'No');
echo "User type: " . ($user ? $user->user_type : 'N/A');
```

## Common Issues and Solutions

### Issue 1: Middleware Not Found
**Error**: `Class 'App\Http\Middleware\AdminOnly' not found`

**Solution**:
```bash
composer dump-autoload
php artisan config:clear
```

### Issue 2: Middleware Not Registered
**Error**: `Route [admin.only] not defined`

**Solution**: Verify `app/Http/Kernel.php` has the middleware registered:
```php
'admin.only' => \App\Http\Middleware\AdminOnly::class,
```

### Issue 3: Config Cache Issues
**Error**: Old configuration still being used

**Solution**:
```bash
php artisan config:clear
php artisan config:cache  # Only if you use config caching
```

### Issue 4: Admin User Not Properly Set
**Error**: User exists but middleware still blocks access

**Solution**: Verify admin user in database:
```sql
SELECT id, email, user_type FROM users WHERE user_type = 'admin';
```

## Quick Production Fix Commands

Run these commands in sequence on production:

```bash
# 1. Ensure files are deployed (check with git)
git status
git log --oneline -5

# 2. Clear all caches
php artisan config:clear && php artisan cache:clear && php artisan route:clear && php artisan view:clear

# 3. Dump autoloader
composer dump-autoload

# 4. Test admin login
# Login via browser to /admin/login

# 5. Test log-viewer access
# Try accessing /log-viewer after admin login
```

## Verification Steps

After running the fix commands:

1. **Login as admin** at `/admin/login`
2. **Access log-viewer** at `/log-viewer`
3. **Verify 403 for non-admin** (test with regular user or logout)

## Emergency Rollback

If you need to temporarily disable admin-only restriction:

```bash
# Edit config/log-viewer.php
# Change:
'middleware' => [
    'web',
    // 'admin.only',  // Comment this out temporarily
    \Opcodes\LogViewer\Http\Middleware\AuthorizeLogViewer::class,
],

# Then clear config
php artisan config:clear
```

## Contact Information

If issues persist, check:
1. Server error logs (`/var/log/nginx/error.log` or `/var/log/apache2/error.log`)
2. PHP error logs
3. Laravel logs in `storage/logs/`

---

**Remember**: Remove the test route after troubleshooting!
