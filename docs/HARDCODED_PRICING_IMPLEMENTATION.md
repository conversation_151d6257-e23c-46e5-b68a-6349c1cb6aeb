# Hardcoded Reach-Based Pricing Implementation

## Overview

This document describes the hardcoded implementation of reach-based pricing for influencer campaigns. This is a semi-temporary solution that allows immediate deployment of the new pricing system while maintaining the database structure for future admin interface implementation.

## Implementation Details

### 1. HardcodedPricingHelper Class

**Location**: `app/Helpers/HardcodedPricingHelper.php`

This helper class contains all pricing data as private static arrays and provides accessor methods to retrieve pricing information based on follower count and campaign type.

### 2. Pricing Data Structure

#### Story Campaigns (Survey & Boost Me)
| Category | Follower Range | Estimated Reach Rate | CPT (€/1K Followers) |
|----------|----------------|---------------------|---------------------|
| Nano | 1,000 - 10,000 | 6% | 5.00€ |
| Micro | 10,000 - 50,000 | 5% | 4.50€ |
| Mid-tier | 50,000 - 500,000 | 4% | 3.00€ |
| Macro | 500,000 - 1M | 3% | 2.50€ |
| Mega/Celebrity | 1M+ | 2% | 2.00€ |

#### Reel Campaigns (Reaction Video)
| Category | Follower Range | Estimated Reach Rate | CPT (€/1K Followers) |
|----------|----------------|---------------------|---------------------|
| Nano | 1,000 - 10,000 | 11% | 14.00€ |
| Micro | 10,000 - 50,000 | 10% | 13.00€ |
| Mid-tier | 50,000 - 500,000 | 9% | 12.00€ |
| Macro | 500,000 - 1M | 8% | 11.00€ |
| Mega/Celebrity | 1M+ | 7% | 10.00€ |

### 3. Key Methods

#### Core Pricing Methods
```php
// Get complete pricing data for a campaign type and follower count
HardcodedPricingHelper::getPricingData(string $campaignType, int $followers): ?array

// Get estimated reach rate only
HardcodedPricingHelper::getEstimatedReachRate(string $campaignType, int $followers): ?float

// Get CPT only
HardcodedPricingHelper::getCpt(string $campaignType, int $followers): ?float

// Get influencer category
HardcodedPricingHelper::getInfluencerCategory(int $followers): ?string
```

#### Utility Methods
```php
// Check if follower count is supported (minimum 1,000)
HardcodedPricingHelper::isFollowerCountSupported(int $followers): bool

// Get pricing summary for debugging
HardcodedPricingHelper::getPricingSummary(string $campaignType, int $followers): array

// Get all categories overview
HardcodedPricingHelper::getAllCategories(): array

// Check if hardcoded pricing should be used (always true for now)
HardcodedPricingHelper::shouldUseHardcodedPricing(): bool
```

### 4. Integration with Existing Services

#### ReachBasedPriceCalculatorService Updates

The service has been updated to:
1. **Check follower count support** before attempting pricing calculations
2. **Use hardcoded pricing** as the primary source for estimated reach rates
3. **Maintain database fallback** for future compatibility
4. **Provide enhanced debug information** including pricing category and hardcoded data

#### Key Changes:
```php
// New method with hardcoded fallback
protected function getEstimatedReachPercentageWithFallback(
    string $media, 
    string $campaignType, 
    string $country, 
    int $followers
): ?float

// Enhanced debug information
'debug_info' => [
    // ... existing fields ...
    'pricing_summary' => $pricingSummary,
    'using_hardcoded_pricing' => true,
]
```

### 5. Campaign Type Mapping

The system maps campaign types to post types and pricing tables:

| Campaign Type | Post Type | Pricing Table |
|---------------|-----------|---------------|
| Boost me | Story | Story Pricing |
| Survey | Story | Story Pricing |
| Reaction video | Reel | Reel Pricing |

### 6. Boundary Handling

The system handles follower count boundaries correctly:
- **10,000 followers**: Classified as Micro (not Nano)
- **50,000 followers**: Classified as Mid-tier (not Micro)
- **500,000 followers**: Classified as Macro (not Mid-tier)
- **1,000,000 followers**: Classified as Mega (not Macro)

### 7. Error Handling

The system gracefully handles edge cases:
- **Below 1,000 followers**: Returns null, falls back to standard pricing
- **Unknown campaign types**: Returns null, falls back to standard pricing
- **No recent posts**: Average reach becomes 0, falls back to standard pricing

### 8. Example Calculations

#### Nano Influencer - Boost Me Campaign
- **Followers**: 5,000
- **Campaign**: Boost me (Story)
- **Hardcoded Data**: 6% reach rate, 5.00€ CPT
- **Average Reach**: 300 (from recent posts)
- **Expected Reach**: 5,000 × 0.06 = 300
- **Reach Multiplier**: 300 ÷ 300 = 1.0
- **Base Price**: 5.00€ × 5 = 25.00€
- **Final Price**: 25.00€ × 1.0 = 25.00€ (before VAT/CIF)

#### Micro Influencer - Reaction Video Campaign
- **Followers**: 25,000
- **Campaign**: Reaction video (Reel)
- **Hardcoded Data**: 10% reach rate, 13.00€ CPT
- **Average Reach**: 3,000 (from recent posts)
- **Expected Reach**: 25,000 × 0.10 = 2,500
- **Reach Multiplier**: 3,000 ÷ 2,500 = 1.2
- **Base Price**: 13.00€ × 25 = 325.00€
- **Final Price**: 325.00€ × 1.2 = 390.00€ (before VAT/CIF)

### 9. Testing

Comprehensive test coverage includes:
- **Unit Tests**: `HardcodedPricingHelperTest` (15 test cases)
- **Integration Tests**: `HardcodedReachBasedPricingTest` (6 test scenarios)
- **Boundary Testing**: Exact follower count boundaries
- **Error Handling**: Unsupported follower counts and unknown campaign types

### 10. API Response Enhancement

The pricing API now includes additional debug information:

```json
{
  "code": "200",
  "price": "23.80",
  "price_next": "30.94",
  "reach_multiplier": 1.0,
  "used_reach_pricing": true,
  "debug_info": {
    "using_hardcoded_pricing": true,
    "pricing_summary": {
      "campaign_type": "Boost me",
      "followers": 5000,
      "category": "Nano",
      "estimated_reach_rate": 6.0,
      "cpt": 5.0,
      "follower_range": {
        "min": 1000,
        "max": 10000
      }
    }
  }
}
```

### 11. Future Migration Path

When implementing the admin interface:

1. **Database Priority**: The system will check database first, then fall back to hardcoded data
2. **Gradual Migration**: Admin can configure pricing for specific tiers while others use hardcoded data
3. **Seamless Transition**: No code changes needed in controllers or API responses
4. **Data Validation**: Admin interface can use hardcoded data as default values

### 12. Advantages of This Approach

- ✅ **Immediate Deployment**: No need to wait for admin interface development
- ✅ **Data Integrity**: Pricing data is version-controlled and consistent
- ✅ **Performance**: No database queries for pricing lookups
- ✅ **Testing**: Easy to test with predictable data
- ✅ **Future-Proof**: Database structure ready for admin interface
- ✅ **Fallback Safety**: Always has pricing data available

### 13. Configuration Management

To update pricing data:
1. Edit `app/Helpers/HardcodedPricingHelper.php`
2. Update the private static arrays
3. Run tests to ensure consistency
4. Deploy changes

### 14. Monitoring

The system provides comprehensive logging and debug information to monitor:
- Which pricing source is being used (hardcoded vs database)
- Pricing calculations and multipliers
- Follower categorization
- Reach calculation details

This implementation provides a robust, testable, and maintainable solution for reach-based pricing while maintaining flexibility for future enhancements.
