# Log Viewer Admin Access Setup

## Overview
The Log Viewer package has been configured to be accessible only to admin users. This prevents unauthorized access to sensitive log information in production.

## Configuration Changes Made

### 1. Created Admin-Only Middleware
**File**: `app/Http/Middleware/AdminOnly.php`

This middleware checks if the user is authenticated as an admin through either:
- Admin guard (`auth:admin`)
- Web guard with `user_type === 'admin'`

### 2. Registered Middleware
**File**: `app/Http/Kernel.php`
- Added `'admin.only' => \App\Http\Middleware\AdminOnly::class` to route middleware

### 3. Updated Log Viewer Configuration
**File**: `config/log-viewer.php`
- Added `'admin.only'` middleware to both `middleware` and `api_middleware` arrays
- This ensures both the web interface and API endpoints are protected

## How to Access Log Viewer

### Step 1: Login as Admin
1. Go to `/admin/login`
2. Login with admin credentials
3. Ensure your user account has `user_type = 'admin'`

### Step 2: Access Log Viewer
Once logged in as admin, you can access:
- **Log Viewer Interface**: `/log-viewer`

## Security Features

### Access Control
- ✅ Only admin users can access log viewer
- ✅ 403 Forbidden error for non-admin users
- ✅ Both web interface and API are protected
- ✅ Works with existing admin authentication system

### Error Handling
- Non-admin users receive: "Access denied. Admin privileges required."
- Unauthenticated users are redirected to login

## Troubleshooting

### 403 Forbidden Error
If you're getting a 403 error, check:

1. **User Authentication**: Ensure you're logged in
2. **User Type**: Verify your user has `user_type = 'admin'`
3. **Admin Guard**: Make sure admin authentication is working
4. **Cache**: Clear application cache if needed:
   ```bash
   php artisan config:clear
   php artisan cache:clear
   ```

### Verify Admin Status
You can check if a user is admin by running:
```php
// In tinker or controller
$user = Auth::user();
echo "User Type: " . $user->user_type;
echo "Is Admin: " . ($user->user_type === 'admin' ? 'Yes' : 'No');
```

## Admin User Management

### Creating Admin Users
To create an admin user, ensure the user record has:
```php
[
    'user_type' => 'admin',
    // ... other user fields
]
```

### Existing Admin Routes
The application already has admin routes at:
- Login: `/admin/login`
- Dashboard: `/admin/` (redirects to admin.dashboard)
- Logout: `/admin/logout`

## Production Deployment

### Environment Variables
Ensure these are set in production `.env`:
```env
LOG_VIEWER_ENABLED=true
LOG_VIEWER_API_ONLY=false
```

### Security Recommendations
1. **Regular Access Review**: Periodically review who has admin access
2. **Log Monitoring**: Monitor log viewer access in application logs
3. **Strong Passwords**: Ensure admin accounts use strong passwords
4. **Session Security**: Configure secure session settings for admin users

## Testing the Setup

### Test Admin Access
```bash
# Test as admin user
curl -H "Cookie: your-admin-session-cookie" http://your-domain/log-viewer
# Should return 200 OK with log viewer interface

# Test as non-admin user
curl -H "Cookie: your-regular-user-cookie" http://your-domain/log-viewer
# Should return 403 Forbidden
```

### Verify Configuration
```bash
# Check middleware registration
php artisan route:list | grep log-viewer

# Check config
php artisan config:show log-viewer.middleware
```

## Support

If you encounter issues:
1. Check Laravel logs in `storage/logs/`
2. Verify admin authentication is working on other admin routes
3. Ensure middleware is properly registered and cached
4. Test with a known admin user account

---

**Last Updated**: August 2025  
**Package Version**: opcodesio/log-viewer v3.17+
