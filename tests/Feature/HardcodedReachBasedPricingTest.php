<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\SocialConnect;
use App\Models\SocialPost;
use App\Models\AdminGamification;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;

class HardcodedReachBasedPricingTest extends TestCase
{
    use RefreshDatabase;

    protected $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create([
            'trophy' => 'Bronze',
            'country' => 'Standard',
            'is_small_business_owner' => false,
        ]);

        // Create gamification data
        AdminGamification::factory()->create([
            'select_type' => 'Pricing & Rank',
            'type' => 'Bronze',
            'pricing' => 100, // 100% multiplier
        ]);

        AdminGamification::factory()->create([
            'select_type' => 'Pricing & Rank',
            'type' => 'Silver',
            'pricing' => 130, // 130% multiplier for next level
        ]);
    }

    /** @test */
    public function it_uses_hardcoded_pricing_for_nano_influencer_boost_me_campaign()
    {
        // Setup nano influencer (5,000 followers)
        SocialConnect::factory()->create([
            'user_id' => $this->user->id,
            'media' => 'instagram',
            'followers' => 5000, // Nano: 1K-10K, 6% reach rate, 5.00€ CPT
        ]);

        // Create recent story posts with reach data
        SocialPost::factory()->create([
            'user_id' => $this->user->id,
            'post_category' => 'story',
            'published_at' => Carbon::now()->subHours(12),
            'insights' => ['reach' => 400] // Higher than expected reach
        ]);

        SocialPost::factory()->create([
            'user_id' => $this->user->id,
            'post_category' => 'story',
            'published_at' => Carbon::now()->subHours(6),
            'insights' => ['reach' => 200] // Lower than expected reach
        ]);

        // Make API request
        $response = $this->actingAs($this->user)
            ->postJson('/getAdminPricing', [
                'media' => 'instagram',
                'type' => 'Boost me'
            ]);

        $response->assertStatus(200);
        
        $data = $response->json();
        
        // Verify hardcoded reach-based pricing was applied
        $this->assertTrue($data['used_reach_pricing']);
        
        // Expected calculation:
        // Average reach: (400 + 200) / 2 = 300
        // Expected reach: 5000 * 0.06 = 300
        // Reach multiplier: 300 / 300 = 1.0
        $this->assertEquals(1.0, $data['reach_multiplier']);
        
        // Expected price calculation:
        // Base CPT price: 5.00 * (5000/1000) = 25.0
        // After gamification: 25.0 * 1.0 = 25.0
        // After reach adjustment: 25.0 * 1.0 = 25.0
        // After VAT (19%): 25.0 * 1.19 = 29.75
        // After CIF provision (80%): 29.75 * 0.8 = 23.80
        $this->assertEquals('23.80', $data['price']);
        
        // Verify debug info includes hardcoded pricing information
        if (config('app.debug')) {
            $this->assertTrue($data['debug_info']['using_hardcoded_pricing']);
            $this->assertEquals('Nano', $data['debug_info']['pricing_summary']['category']);
            $this->assertEquals(6.0, $data['debug_info']['pricing_summary']['estimated_reach_rate']);
            $this->assertEquals(5.00, $data['debug_info']['pricing_summary']['cpt']);
        }
    }

    /** @test */
    public function it_uses_hardcoded_pricing_for_micro_influencer_reaction_video()
    {
        // Setup micro influencer (25,000 followers)
        SocialConnect::factory()->create([
            'user_id' => $this->user->id,
            'media' => 'instagram',
            'followers' => 25000, // Micro: 10K-50K, 10% reach rate, 13.00€ CPT
        ]);

        // Create recent reel posts with reach data
        SocialPost::factory()->create([
            'user_id' => $this->user->id,
            'post_category' => 'reel',
            'published_at' => Carbon::now()->subHours(8),
            'insights' => ['reach' => 3000] // Higher than expected reach (25000 * 0.10 = 2500)
        ]);

        // Make API request
        $response = $this->actingAs($this->user)
            ->postJson('/getAdminPricing', [
                'media' => 'instagram',
                'type' => 'Reaction video'
            ]);

        $response->assertStatus(200);
        
        $data = $response->json();
        
        // Verify hardcoded reach-based pricing was applied
        $this->assertTrue($data['used_reach_pricing']);
        
        // Expected calculation:
        // Average reach: 3000
        // Expected reach: 25000 * 0.10 = 2500
        // Reach multiplier: 3000 / 2500 = 1.2
        $this->assertEquals(1.2, $data['reach_multiplier']);
        
        // Expected price calculation:
        // Base CPT price: 13.00 * (25000/1000) = 325.0
        // After gamification: 325.0 * 1.0 = 325.0
        // After reach adjustment: 325.0 * 1.2 = 390.0
        // After VAT (19%): 390.0 * 1.19 = 464.1
        // After CIF provision (80%): 464.1 * 0.8 = 371.28
        $this->assertEquals('371.28', $data['price']);
    }

    /** @test */
    public function it_falls_back_to_standard_pricing_for_unsupported_follower_count()
    {
        // Setup influencer with followers below minimum (500 followers)
        SocialConnect::factory()->create([
            'user_id' => $this->user->id,
            'media' => 'instagram',
            'followers' => 500, // Below minimum of 1,000
        ]);

        // Create recent story posts
        SocialPost::factory()->create([
            'user_id' => $this->user->id,
            'post_category' => 'story',
            'published_at' => Carbon::now()->subHours(12),
            'insights' => ['reach' => 100]
        ]);

        // Make API request
        $response = $this->actingAs($this->user)
            ->postJson('/getAdminPricing', [
                'media' => 'instagram',
                'type' => 'Boost me'
            ]);

        $response->assertStatus(200);
        
        $data = $response->json();
        
        // Verify reach-based pricing was NOT applied due to unsupported follower count
        $this->assertFalse($data['used_reach_pricing']);
        $this->assertEquals(1.0, $data['reach_multiplier']);
        
        if (config('app.debug')) {
            $this->assertStringContains('Follower count below minimum', $data['debug_info']['reason']);
        }
    }

    /** @test */
    public function it_handles_mega_influencer_with_low_reach_multiplier()
    {
        // Setup mega influencer (2M followers)
        SocialConnect::factory()->create([
            'user_id' => $this->user->id,
            'media' => 'instagram',
            'followers' => 2000000, // Mega: 1M+, 2% reach rate, 2.00€ CPT
        ]);

        // Create recent story posts with lower than expected reach
        SocialPost::factory()->create([
            'user_id' => $this->user->id,
            'post_category' => 'story',
            'published_at' => Carbon::now()->subHours(4),
            'insights' => ['reach' => 30000] // Lower than expected (2M * 0.02 = 40K)
        ]);

        // Make API request
        $response = $this->actingAs($this->user)
            ->postJson('/getAdminPricing', [
                'media' => 'instagram',
                'type' => 'Boost me'
            ]);

        $response->assertStatus(200);
        
        $data = $response->json();
        
        // Verify hardcoded reach-based pricing was applied
        $this->assertTrue($data['used_reach_pricing']);
        
        // Expected calculation:
        // Average reach: 30000
        // Expected reach: 2000000 * 0.02 = 40000
        // Reach multiplier: 30000 / 40000 = 0.75
        $this->assertEquals(0.75, $data['reach_multiplier']);
        
        // Expected price calculation:
        // Base CPT price: 2.00 * (2000000/1000) = 4000.0
        // After gamification: 4000.0 * 1.0 = 4000.0
        // After reach adjustment: 4000.0 * 0.75 = 3000.0
        // After VAT (19%): 3000.0 * 1.19 = 3570.0
        // After CIF provision (80%): 3570.0 * 0.8 = 2856.00
        $this->assertEquals('2856.00', $data['price']);
        
        if (config('app.debug')) {
            $this->assertEquals('Mega / Celebrity', $data['debug_info']['pricing_summary']['category']);
            $this->assertEquals(2.0, $data['debug_info']['pricing_summary']['estimated_reach_rate']);
            $this->assertEquals(2.00, $data['debug_info']['pricing_summary']['cpt']);
        }
    }

    /** @test */
    public function it_handles_survey_campaign_same_as_boost_me()
    {
        // Setup nano influencer
        SocialConnect::factory()->create([
            'user_id' => $this->user->id,
            'media' => 'instagram',
            'followers' => 5000,
        ]);

        // Create recent story posts
        SocialPost::factory()->create([
            'user_id' => $this->user->id,
            'post_category' => 'story',
            'published_at' => Carbon::now()->subHours(12),
            'insights' => ['reach' => 300]
        ]);

        // Test Survey campaign
        $response = $this->actingAs($this->user)
            ->postJson('/getAdminPricing', [
                'media' => 'instagram',
                'type' => 'Survey'
            ]);

        $response->assertStatus(200);
        
        $data = $response->json();
        
        // Should use same pricing as Boost Me (story campaigns)
        $this->assertTrue($data['used_reach_pricing']);
        
        if (config('app.debug')) {
            $this->assertEquals('Survey', $data['debug_info']['pricing_summary']['campaign_type']);
            $this->assertEquals('Nano', $data['debug_info']['pricing_summary']['category']);
            $this->assertEquals(6.0, $data['debug_info']['pricing_summary']['estimated_reach_rate']);
            $this->assertEquals(5.00, $data['debug_info']['pricing_summary']['cpt']);
        }
    }
}
