<?php

namespace Tests\Unit\Integration;

use Tests\TestCase;
use App\Helpers\HardcodedPricingHelper;

class HardcodedPricingIntegrationTest extends TestCase
{
    /** @test */
    public function it_provides_correct_pricing_data_for_all_campaign_types_and_influencer_tiers()
    {
        // Test all combinations to ensure data integrity
        $testCases = [
            // Nano influencers
            ['followers' => 5000, 'campaign' => 'Boost me', 'expected_category' => 'nano', 'expected_reach' => 6.0, 'expected_cpt' => 5.00],
            ['followers' => 5000, 'campaign' => 'Survey', 'expected_category' => 'nano', 'expected_reach' => 6.0, 'expected_cpt' => 5.00],
            ['followers' => 5000, 'campaign' => 'Reaction video', 'expected_category' => 'nano', 'expected_reach' => 11.0, 'expected_cpt' => 14.00],
            
            // Micro influencers
            ['followers' => 25000, 'campaign' => 'Boost me', 'expected_category' => 'micro', 'expected_reach' => 5.0, 'expected_cpt' => 4.50],
            ['followers' => 25000, 'campaign' => 'Survey', 'expected_category' => 'micro', 'expected_reach' => 5.0, 'expected_cpt' => 4.50],
            ['followers' => 25000, 'campaign' => 'Reaction video', 'expected_category' => 'micro', 'expected_reach' => 10.0, 'expected_cpt' => 13.00],
            
            // Mid-tier influencers
            ['followers' => 100000, 'campaign' => 'Boost me', 'expected_category' => 'mid_tier', 'expected_reach' => 4.0, 'expected_cpt' => 3.00],
            ['followers' => 100000, 'campaign' => 'Survey', 'expected_category' => 'mid_tier', 'expected_reach' => 4.0, 'expected_cpt' => 3.00],
            ['followers' => 100000, 'campaign' => 'Reaction video', 'expected_category' => 'mid_tier', 'expected_reach' => 9.0, 'expected_cpt' => 12.00],
            
            // Macro influencers
            ['followers' => 750000, 'campaign' => 'Boost me', 'expected_category' => 'macro', 'expected_reach' => 3.0, 'expected_cpt' => 2.50],
            ['followers' => 750000, 'campaign' => 'Survey', 'expected_category' => 'macro', 'expected_reach' => 3.0, 'expected_cpt' => 2.50],
            ['followers' => 750000, 'campaign' => 'Reaction video', 'expected_category' => 'macro', 'expected_reach' => 8.0, 'expected_cpt' => 11.00],
            
            // Mega influencers
            ['followers' => 2000000, 'campaign' => 'Boost me', 'expected_category' => 'mega', 'expected_reach' => 2.0, 'expected_cpt' => 2.00],
            ['followers' => 2000000, 'campaign' => 'Survey', 'expected_category' => 'mega', 'expected_reach' => 2.0, 'expected_cpt' => 2.00],
            ['followers' => 2000000, 'campaign' => 'Reaction video', 'expected_category' => 'mega', 'expected_reach' => 7.0, 'expected_cpt' => 10.00],
        ];

        foreach ($testCases as $case) {
            $data = HardcodedPricingHelper::getPricingData($case['campaign'], $case['followers']);
            
            $this->assertNotNull($data, "Failed to get pricing data for {$case['followers']} followers, {$case['campaign']} campaign");
            $this->assertEquals($case['expected_category'], $data['category'], "Wrong category for {$case['followers']} followers, {$case['campaign']} campaign");
            $this->assertEquals($case['expected_reach'], $data['estimated_reach_rate'], "Wrong reach rate for {$case['followers']} followers, {$case['campaign']} campaign");
            $this->assertEquals($case['expected_cpt'], $data['cpt'], "Wrong CPT for {$case['followers']} followers, {$case['campaign']} campaign");
        }
    }

    /** @test */
    public function it_calculates_realistic_pricing_examples()
    {
        // Test realistic pricing calculations
        $examples = [
            [
                'name' => 'Nano Influencer - Boost Me',
                'followers' => 5000,
                'campaign' => 'Boost me',
                'expected_base_price' => 25.00, // 5.00 CPT * 5 (thousands)
                'expected_reach_rate' => 6.0,
                'expected_reach_at_rate' => 300, // 5000 * 0.06
            ],
            [
                'name' => 'Micro Influencer - Reaction Video',
                'followers' => 25000,
                'campaign' => 'Reaction video',
                'expected_base_price' => 325.00, // 13.00 CPT * 25 (thousands)
                'expected_reach_rate' => 10.0,
                'expected_reach_at_rate' => 2500, // 25000 * 0.10
            ],
            [
                'name' => 'Mega Influencer - Survey',
                'followers' => 2000000,
                'campaign' => 'Survey',
                'expected_base_price' => 4000.00, // 2.00 CPT * 2000 (thousands)
                'expected_reach_rate' => 2.0,
                'expected_reach_at_rate' => 40000, // 2000000 * 0.02
            ],
        ];

        foreach ($examples as $example) {
            $data = HardcodedPricingHelper::getPricingData($example['campaign'], $example['followers']);
            
            $this->assertNotNull($data, "Failed to get data for {$example['name']}");
            
            // Calculate base price
            $calculatedBasePrice = $data['cpt'] * ($example['followers'] / 1000);
            $this->assertEquals($example['expected_base_price'], $calculatedBasePrice, "Wrong base price calculation for {$example['name']}");
            
            // Verify reach rate
            $this->assertEquals($example['expected_reach_rate'], $data['estimated_reach_rate'], "Wrong reach rate for {$example['name']}");
            
            // Calculate expected reach at the given rate
            $expectedReachAtRate = $example['followers'] * ($data['estimated_reach_rate'] / 100);
            $this->assertEquals($example['expected_reach_at_rate'], $expectedReachAtRate, "Wrong expected reach calculation for {$example['name']}");
        }
    }

    /** @test */
    public function it_handles_boundary_cases_correctly()
    {
        $boundaryCases = [
            // Test exact boundaries
            ['followers' => 1000, 'expected_category' => 'nano'],
            ['followers' => 9999, 'expected_category' => 'nano'],
            ['followers' => 10000, 'expected_category' => 'micro'],
            ['followers' => 49999, 'expected_category' => 'micro'],
            ['followers' => 50000, 'expected_category' => 'mid_tier'],
            ['followers' => 499999, 'expected_category' => 'mid_tier'],
            ['followers' => 500000, 'expected_category' => 'macro'],
            ['followers' => 999999, 'expected_category' => 'macro'],
            ['followers' => 1000000, 'expected_category' => 'mega'],
            ['followers' => 5000000, 'expected_category' => 'mega'],
        ];

        foreach ($boundaryCases as $case) {
            $category = HardcodedPricingHelper::getInfluencerCategory($case['followers']);
            $this->assertEquals($case['expected_category'], $category, "Wrong category for {$case['followers']} followers");
        }
    }

    /** @test */
    public function it_provides_comprehensive_pricing_overview()
    {
        $categories = HardcodedPricingHelper::getAllCategories();
        
        // Verify all expected categories are present
        $expectedCategories = ['nano', 'micro', 'mid_tier', 'macro', 'mega'];
        foreach ($expectedCategories as $expectedCategory) {
            $this->assertArrayHasKey($expectedCategory, $categories, "Missing category: {$expectedCategory}");
        }

        // Verify each category has all required data
        foreach ($categories as $categoryKey => $categoryData) {
            $this->assertArrayHasKey('name', $categoryData, "Missing name for category: {$categoryKey}");
            $this->assertArrayHasKey('min_followers', $categoryData, "Missing min_followers for category: {$categoryKey}");
            $this->assertArrayHasKey('max_followers', $categoryData, "Missing max_followers for category: {$categoryKey}");
            $this->assertArrayHasKey('story_reach_rate', $categoryData, "Missing story_reach_rate for category: {$categoryKey}");
            $this->assertArrayHasKey('story_cpt', $categoryData, "Missing story_cpt for category: {$categoryKey}");
            $this->assertArrayHasKey('reel_reach_rate', $categoryData, "Missing reel_reach_rate for category: {$categoryKey}");
            $this->assertArrayHasKey('reel_cpt', $categoryData, "Missing reel_cpt for category: {$categoryKey}");
        }

        // Verify logical progression (higher tiers should have lower reach rates but different CPT patterns)
        $this->assertTrue($categories['nano']['story_reach_rate'] > $categories['mega']['story_reach_rate'], 
            "Nano influencers should have higher story reach rate than mega influencers");
        $this->assertTrue($categories['nano']['reel_reach_rate'] > $categories['mega']['reel_reach_rate'], 
            "Nano influencers should have higher reel reach rate than mega influencers");
    }

    /** @test */
    public function it_validates_data_consistency()
    {
        // Verify that story campaigns (Boost me, Survey) use the same pricing
        $followers = 25000;
        
        $boostMeData = HardcodedPricingHelper::getPricingData('Boost me', $followers);
        $surveyData = HardcodedPricingHelper::getPricingData('Survey', $followers);
        
        $this->assertEquals($boostMeData['estimated_reach_rate'], $surveyData['estimated_reach_rate'], 
            "Boost me and Survey should have same reach rate");
        $this->assertEquals($boostMeData['cpt'], $surveyData['cpt'], 
            "Boost me and Survey should have same CPT");
        $this->assertEquals($boostMeData['category'], $surveyData['category'], 
            "Boost me and Survey should have same category");

        // Verify that Reaction video has different (higher) pricing
        $reactionVideoData = HardcodedPricingHelper::getPricingData('Reaction video', $followers);
        
        $this->assertNotEquals($boostMeData['estimated_reach_rate'], $reactionVideoData['estimated_reach_rate'], 
            "Reaction video should have different reach rate than story campaigns");
        $this->assertNotEquals($boostMeData['cpt'], $reactionVideoData['cpt'], 
            "Reaction video should have different CPT than story campaigns");
        $this->assertTrue($reactionVideoData['cpt'] > $boostMeData['cpt'], 
            "Reaction video CPT should be higher than story campaigns");
    }

    /** @test */
    public function it_handles_edge_cases_gracefully()
    {
        // Test unsupported follower counts
        $this->assertNull(HardcodedPricingHelper::getPricingData('Boost me', 500), 
            "Should return null for followers below minimum");
        $this->assertFalse(HardcodedPricingHelper::isFollowerCountSupported(500), 
            "Should not support followers below 1000");

        // Test unknown campaign types
        $this->assertNull(HardcodedPricingHelper::getPricingData('Unknown Campaign', 5000), 
            "Should return null for unknown campaign types");

        // Test minimum supported follower count
        $this->assertTrue(HardcodedPricingHelper::isFollowerCountSupported(1000), 
            "Should support exactly 1000 followers");
        $this->assertNotNull(HardcodedPricingHelper::getPricingData('Boost me', 1000), 
            "Should provide pricing for exactly 1000 followers");
    }

    /** @test */
    public function it_always_uses_hardcoded_pricing_for_now()
    {
        $this->assertTrue(HardcodedPricingHelper::shouldUseHardcodedPricing(), 
            "Should always use hardcoded pricing in current implementation");
    }
}
