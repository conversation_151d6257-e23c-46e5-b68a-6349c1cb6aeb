<?php

namespace Tests\Unit\Helpers;

use Tests\TestCase;
use App\Helpers\HardcodedPricingHelper;

class HardcodedPricingHelperTest extends TestCase
{
    /** @test */
    public function it_returns_correct_pricing_for_nano_influencer_story_campaigns()
    {
        $followers = 5000; // Nano range: 1,000 - 10,000
        
        // Test Boost Me campaign
        $boostMeData = HardcodedPricingHelper::getPricingData('Boost me', $followers);
        $this->assertEquals('nano', $boostMeData['category']);
        $this->assertEquals(6.0, $boostMeData['estimated_reach_rate']);
        $this->assertEquals(5.00, $boostMeData['cpt']);
        
        // Test Survey campaign
        $surveyData = HardcodedPricingHelper::getPricingData('Survey', $followers);
        $this->assertEquals('nano', $surveyData['category']);
        $this->assertEquals(6.0, $surveyData['estimated_reach_rate']);
        $this->assertEquals(5.00, $surveyData['cpt']);
    }

    /** @test */
    public function it_returns_correct_pricing_for_nano_influencer_reel_campaigns()
    {
        $followers = 5000; // Nano range: 1,000 - 10,000
        
        $reactionVideoData = HardcodedPricingHelper::getPricingData('Reaction video', $followers);
        $this->assertEquals('nano', $reactionVideoData['category']);
        $this->assertEquals(11.0, $reactionVideoData['estimated_reach_rate']);
        $this->assertEquals(14.00, $reactionVideoData['cpt']);
    }

    /** @test */
    public function it_returns_correct_pricing_for_micro_influencer()
    {
        $followers = 25000; // Micro range: 10,000 - 50,000
        
        // Story campaigns
        $boostMeData = HardcodedPricingHelper::getPricingData('Boost me', $followers);
        $this->assertEquals('micro', $boostMeData['category']);
        $this->assertEquals(5.0, $boostMeData['estimated_reach_rate']);
        $this->assertEquals(4.50, $boostMeData['cpt']);
        
        // Reel campaigns
        $reactionVideoData = HardcodedPricingHelper::getPricingData('Reaction video', $followers);
        $this->assertEquals('micro', $reactionVideoData['category']);
        $this->assertEquals(10.0, $reactionVideoData['estimated_reach_rate']);
        $this->assertEquals(13.00, $reactionVideoData['cpt']);
    }

    /** @test */
    public function it_returns_correct_pricing_for_mid_tier_influencer()
    {
        $followers = 100000; // Mid-tier range: 50,000 - 500,000
        
        $boostMeData = HardcodedPricingHelper::getPricingData('Boost me', $followers);
        $this->assertEquals('mid_tier', $boostMeData['category']);
        $this->assertEquals(4.0, $boostMeData['estimated_reach_rate']);
        $this->assertEquals(3.00, $boostMeData['cpt']);
    }

    /** @test */
    public function it_returns_correct_pricing_for_macro_influencer()
    {
        $followers = 750000; // Macro range: 500,000 - 1,000,000
        
        $boostMeData = HardcodedPricingHelper::getPricingData('Boost me', $followers);
        $this->assertEquals('macro', $boostMeData['category']);
        $this->assertEquals(3.0, $boostMeData['estimated_reach_rate']);
        $this->assertEquals(2.50, $boostMeData['cpt']);
    }

    /** @test */
    public function it_returns_correct_pricing_for_mega_influencer()
    {
        $followers = 2000000; // Mega range: 1,000,000+
        
        $boostMeData = HardcodedPricingHelper::getPricingData('Boost me', $followers);
        $this->assertEquals('mega', $boostMeData['category']);
        $this->assertEquals(2.0, $boostMeData['estimated_reach_rate']);
        $this->assertEquals(2.00, $boostMeData['cpt']);
    }

    /** @test */
    public function it_handles_boundary_cases_correctly()
    {
        // Test exact boundary: 10,000 followers should be micro (not nano)
        $followers = 10000;
        $data = HardcodedPricingHelper::getPricingData('Boost me', $followers);
        $this->assertEquals('micro', $data['category']);
        
        // Test just below boundary: 9,999 followers should be nano
        $followers = 9999;
        $data = HardcodedPricingHelper::getPricingData('Boost me', $followers);
        $this->assertEquals('nano', $data['category']);
        
        // Test 1M boundary: exactly 1,000,000 should be mega
        $followers = 1000000;
        $data = HardcodedPricingHelper::getPricingData('Boost me', $followers);
        $this->assertEquals('mega', $data['category']);
    }

    /** @test */
    public function it_returns_null_for_unsupported_follower_counts()
    {
        $followers = 500; // Below minimum of 1,000
        $data = HardcodedPricingHelper::getPricingData('Boost me', $followers);
        $this->assertNull($data);
    }

    /** @test */
    public function it_returns_null_for_unknown_campaign_types()
    {
        $followers = 5000;
        $data = HardcodedPricingHelper::getPricingData('Unknown Campaign', $followers);
        $this->assertNull($data);
    }

    /** @test */
    public function it_provides_correct_accessor_methods()
    {
        $followers = 25000; // Micro influencer
        
        // Test individual accessors
        $reachRate = HardcodedPricingHelper::getEstimatedReachRate('Boost me', $followers);
        $this->assertEquals(5.0, $reachRate);
        
        $cpt = HardcodedPricingHelper::getCpt('Boost me', $followers);
        $this->assertEquals(4.50, $cpt);
        
        $category = HardcodedPricingHelper::getInfluencerCategory($followers);
        $this->assertEquals('micro', $category);
    }

    /** @test */
    public function it_validates_follower_count_support()
    {
        $this->assertTrue(HardcodedPricingHelper::isFollowerCountSupported(5000));
        $this->assertFalse(HardcodedPricingHelper::isFollowerCountSupported(500));
        $this->assertEquals(1000, HardcodedPricingHelper::getMinimumFollowerCount());
    }

    /** @test */
    public function it_provides_pricing_summary()
    {
        $followers = 25000;
        $summary = HardcodedPricingHelper::getPricingSummary('Boost me', $followers);
        
        $this->assertEquals('Boost me', $summary['campaign_type']);
        $this->assertEquals(25000, $summary['followers']);
        $this->assertEquals('Micro', $summary['category']);
        $this->assertEquals(5.0, $summary['estimated_reach_rate']);
        $this->assertEquals(4.50, $summary['cpt']);
        $this->assertTrue($summary['using_hardcoded_pricing']);
        $this->assertEquals(['min' => 10000, 'max' => 50000], $summary['follower_range']);
    }

    /** @test */
    public function it_provides_all_categories_overview()
    {
        $categories = HardcodedPricingHelper::getAllCategories();
        
        $this->assertCount(5, $categories);
        $this->assertArrayHasKey('nano', $categories);
        $this->assertArrayHasKey('micro', $categories);
        $this->assertArrayHasKey('mid_tier', $categories);
        $this->assertArrayHasKey('macro', $categories);
        $this->assertArrayHasKey('mega', $categories);
        
        // Test nano category structure
        $nano = $categories['nano'];
        $this->assertEquals('Nano', $nano['name']);
        $this->assertEquals(1000, $nano['min_followers']);
        $this->assertEquals(10000, $nano['max_followers']);
        $this->assertEquals(6.0, $nano['story_reach_rate']);
        $this->assertEquals(5.00, $nano['story_cpt']);
        $this->assertEquals(11.0, $nano['reel_reach_rate']);
        $this->assertEquals(14.00, $nano['reel_cpt']);
    }

    /** @test */
    public function it_provides_correct_display_names()
    {
        $this->assertEquals('Nano', HardcodedPricingHelper::getCategoryDisplayName('nano'));
        $this->assertEquals('Micro', HardcodedPricingHelper::getCategoryDisplayName('micro'));
        $this->assertEquals('Mid-tier', HardcodedPricingHelper::getCategoryDisplayName('mid_tier'));
        $this->assertEquals('Macro', HardcodedPricingHelper::getCategoryDisplayName('macro'));
        $this->assertEquals('Mega / Celebrity', HardcodedPricingHelper::getCategoryDisplayName('mega'));
    }

    /** @test */
    public function it_always_uses_hardcoded_pricing_for_now()
    {
        $this->assertTrue(HardcodedPricingHelper::shouldUseHardcodedPricing());
    }
}
